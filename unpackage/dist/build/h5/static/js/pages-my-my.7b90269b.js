(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-my-my"],{"0011":function(t,a,e){"use strict";e("a9e3"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var n={name:"u-row",props:{gutter:{type:[String,Number],default:20},justify:{type:String,default:"start"},align:{type:String,default:"center"},stop:{type:Boolean,default:!0}},computed:{uJustify:function(){return"end"==this.justify||"start"==this.justify?"flex-"+this.justify:"around"==this.justify||"between"==this.justify?"space-"+this.justify:this.justify},uAlignItem:function(){return"top"==this.align?"flex-start":"bottom"==this.align?"flex-end":this.align}},methods:{click:function(t){this.$emit("click")}}};a.default=n},"10ab":function(t,a,e){"use strict";var n;e.d(a,"b",(function(){return i})),e.d(a,"c",(function(){return o})),e.d(a,"a",(function(){return n}));var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{staticClass:"u-col",class:["u-col-"+t.span],style:{padding:"0 "+Number(t.gutter)/2+"rpx",marginLeft:100/12*t.offset+"%",flex:"0 0 "+100/12*t.span+"%",alignItems:t.uAlignItem,justifyContent:t.uJustify,textAlign:t.textAlign},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.click.apply(void 0,arguments)}}},[t._t("default")],2)},o=[]},"29d4":function(t,a,e){var n=e("24fb");a=n(!1),a.push([t.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-0a5981a4]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-0a5981a4]{text-align:center}.pay-modal-amount-value[data-v-0a5981a4]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-0a5981a4]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-0a5981a4]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-0a5981a4]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-0a5981a4]{font-size:%?36?%}.custom-pay-modal-btn[data-v-0a5981a4]{width:80%}.u-col-0[data-v-0a5981a4]{width:0}.u-col-1[data-v-0a5981a4]{width:calc(100%/12)}.u-col-2[data-v-0a5981a4]{width:calc(100%/12 * 2)}.u-col-3[data-v-0a5981a4]{width:calc(100%/12 * 3)}.u-col-4[data-v-0a5981a4]{width:calc(100%/12 * 4)}.u-col-5[data-v-0a5981a4]{width:calc(100%/12 * 5)}.u-col-6[data-v-0a5981a4]{width:calc(100%/12 * 6)}.u-col-7[data-v-0a5981a4]{width:calc(100%/12 * 7)}.u-col-8[data-v-0a5981a4]{width:calc(100%/12 * 8)}.u-col-9[data-v-0a5981a4]{width:calc(100%/12 * 9)}.u-col-10[data-v-0a5981a4]{width:calc(100%/12 * 10)}.u-col-11[data-v-0a5981a4]{width:calc(100%/12 * 11)}.u-col-12[data-v-0a5981a4]{width:calc(100%/12 * 12)}',""]),t.exports=a},"2e49":function(t,a,e){var n=e("29d4");"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=e("4f06").default;i("cf69df92",n,!0,{sourceMap:!1,shadowMode:!1})},"35c9":function(t,a,e){"use strict";var n=e("bf63"),i=e.n(n);i.a},3630:function(t,a,e){"use strict";e.r(a);var n=e("8ee4"),i=e("f225");for(var o in i)"default"!==o&&function(t){e.d(a,t,(function(){return i[t]}))}(o);e("35c9");var s,l=e("f0c5"),c=Object(l["a"])(i["default"],n["b"],n["c"],!1,null,"345affc4",null,!1,n["a"],s);a["default"]=c.exports},"3f20":function(t,a,e){"use strict";var n=e("2e49"),i=e.n(n);i.a},"46f4":function(t,a,e){"use strict";e.r(a);var n=e("10ab"),i=e("7131");for(var o in i)"default"!==o&&function(t){e.d(a,t,(function(){return i[t]}))}(o);e("3f20");var s,l=e("f0c5"),c=Object(l["a"])(i["default"],n["b"],n["c"],!1,null,"0a5981a4",null,!1,n["a"],s);a["default"]=c.exports},"4c99":function(t,a,e){"use strict";e("a9e3"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var n={name:"u-col",props:{span:{type:[Number,String],default:12},offset:{type:[Number,String],default:0},justify:{type:String,default:"start"},align:{type:String,default:"center"},textAlign:{type:String,default:"left"},stop:{type:Boolean,default:!0}},data:function(){return{gutter:20}},created:function(){this.parent=!1},mounted:function(){this.parent=this.$u.$parent.call(this,"u-row"),this.parent&&(this.gutter=this.parent.gutter)},computed:{uJustify:function(){return"end"==this.justify||"start"==this.justify?"flex-"+this.justify:"around"==this.justify||"between"==this.justify?"space-"+this.justify:this.justify},uAlignItem:function(){return"top"==this.align?"flex-start":"bottom"==this.align?"flex-end":this.align}},methods:{click:function(t){this.$emit("click")}}};a.default=n},"58b4":function(t,a,e){"use strict";e.r(a);var n=e("9a4a"),i=e("8304");for(var o in i)"default"!==o&&function(t){e.d(a,t,(function(){return i[t]}))}(o);e("c08e");var s,l=e("f0c5"),c=Object(l["a"])(i["default"],n["b"],n["c"],!1,null,"7b63e37c",null,!1,n["a"],s);a["default"]=c.exports},7131:function(t,a,e){"use strict";e.r(a);var n=e("4c99"),i=e.n(n);for(var o in n)"default"!==o&&function(t){e.d(a,t,(function(){return n[t]}))}(o);a["default"]=i.a},8304:function(t,a,e){"use strict";e.r(a);var n=e("b54f"),i=e.n(n);for(var o in n)"default"!==o&&function(t){e.d(a,t,(function(){return n[t]}))}(o);a["default"]=i.a},"8de2":function(t,a,e){var n=e("24fb");a=n(!1),a.push([t.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-345affc4]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-345affc4]{text-align:center}.pay-modal-amount-value[data-v-345affc4]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-345affc4]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-345affc4]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-345affc4]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-345affc4]{font-size:%?36?%}.custom-pay-modal-btn[data-v-345affc4]{width:80%}.u-row[data-v-345affc4]{display:flex;flex-direction:row;flex-wrap:wrap}',""]),t.exports=a},"8ee4":function(t,a,e){"use strict";var n;e.d(a,"b",(function(){return i})),e.d(a,"c",(function(){return o})),e.d(a,"a",(function(){return n}));var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{staticClass:"u-row",style:{alignItems:t.uAlignItem,justifyContent:t.uJustify},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.click.apply(void 0,arguments)}}},[t._t("default")],2)},o=[]},"94b7":function(t,a,e){var n=e("9cb2");"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=e("4f06").default;i("29b2d460",n,!0,{sourceMap:!1,shadowMode:!1})},"9a4a":function(t,a,e){"use strict";e.d(a,"b",(function(){return i})),e.d(a,"c",(function(){return o})),e.d(a,"a",(function(){return n}));var n={uImage:e("93e8").default,uIcon:e("8ed9").default,uTabs:e("45b0").default,uEmpty:e("b1fc").default,uRow:e("3630").default,uCol:e("46f4").default,uLoadmore:e("6b9c").default},i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{staticClass:"page-content"},[e("v-uni-view",{staticClass:"top-content"},[e("v-uni-view",{staticClass:"avatar-nick-name"},[e("v-uni-view",{staticClass:"member-avatar"},[e("u-image",{attrs:{height:"80rpx",width:"80rpx",src:t.getAvatar(),shape:"circle"}})],1),e("v-uni-view",{staticClass:"nick-name-mobile"},[e("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isLogin(),expression:"isLogin()"}]},[e("v-uni-view",{staticClass:"member-nick-name"},[t._v(t._s(t.personalInfo.nickName))]),e("v-uni-view",{staticClass:"member-mobile"},[t._v(t._s(t.personalInfo.mobile))])],1),e("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!t.isLogin(),expression:"!isLogin()"}],staticClass:"login",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.loginPage.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"login-t"},[t._v("登录/注册")]),e("v-uni-view",{staticClass:"login-b"},[t._v("点击登录 享受更多精彩内容")])],1)],1),e("v-uni-view",{staticClass:"setting",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.gotoPage("../setting/setting")}}},[e("u-icon",{attrs:{name:"setting",size:"48"}})],1)],1),e("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isLogin(),expression:"isLogin()"}],staticClass:"block-chain-addr"},[e("v-uni-text",{staticClass:"block-chain-addr-l"},[t._v("区块链地址："+t._s(t.getBlockChainAddr()))]),e("u-image",{directives:[{name:"show",rawName:"v-show",value:t.personalInfo.blockChainAddr,expression:"personalInfo.blockChainAddr"}],attrs:{height:"32rpx",width:"32rpx",src:"/static/img/copy_my.png"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.copyData(t.personalInfo.blockChainAddr)}}})],1),e("v-uni-view",{staticClass:"grid-navs"},[e("v-uni-view",{staticClass:"grid-nav",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.gotoPage("../balanceChangeLog/balanceChangeLog")}}},[e("u-icon",{attrs:{name:"rmb-circle",size:"48"}}),e("v-uni-view",[t._v("我的钱包")])],1),e("v-uni-view",{staticClass:"grid-nav",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.gotoPage("../payOrder/payOrder")}}},[e("u-icon",{attrs:{name:"order",size:"48"}}),e("v-uni-view",[t._v("我的订单")])],1),e("v-uni-view",{staticClass:"grid-nav",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.gotoPage("../myGiveRecord/myGiveRecord")}}},[e("u-icon",{attrs:{name:"zhuanfa",size:"48"}}),e("v-uni-view",[t._v("转赠记录")])],1),e("v-uni-view",{staticClass:"grid-nav",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.gotoPage("../msg/msg")}}},[e("v-uni-view",{staticClass:"msg-read",class:{"msg-unread-num":t.unreadMsgIds.length>0}}),e("u-icon",{attrs:{name:"chat",size:"48"}}),e("v-uni-view",[t._v("消息通知")])],1)],1),e("v-uni-view",{staticClass:"grid-navs"},[e("v-uni-view",{staticClass:"grid-nav",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.gotoPage("../invite/invite")}}},[e("u-icon",{attrs:{name:"man-add",size:"48"}}),e("v-uni-view",[t._v("邀请好友")])],1),e("v-uni-view",{staticClass:"grid-nav",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.gotoPage("../composeActivity/composeActivity")}}},[e("u-icon",{attrs:{name:"bag",size:"48"}}),e("v-uni-view",[t._v("合成藏品")])],1),e("v-uni-view",{staticClass:"grid-nav",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.gotoPage("../exchangeCode/exchangeCode")}}},[e("u-icon",{attrs:{name:"gift",size:"48"}}),e("v-uni-view",[t._v("空投兑换")])],1),e("v-uni-view",{staticClass:"grid-nav",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.contactCustomerService.apply(void 0,arguments)}}},[e("u-icon",{attrs:{name:"kefu-ermai",size:"48"}}),e("v-uni-view",[t._v("联系客服")])],1)],1)],1),e("v-uni-view",[e("v-uni-view",{staticClass:"custom-tabs"},[e("v-uni-view",{staticClass:"custom-tabs-l"},[e("u-tabs",{attrs:{list:t.tabs,"is-scroll":!1,current:t.currentTab},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.switchTab.apply(void 0,arguments)}}})],1)],1),e("v-uni-view",[e("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.noDataFlag,expression:"noDataFlag"}],staticClass:"no-data"},[e("u-empty",{attrs:{text:"暂无数据",mode:"favor"}})],1),e("v-uni-view",{staticClass:"custom-tab-content"},[e("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:0==t.currentTab,expression:"currentTab == 0"}]},[e("u-row",{attrs:{gutter:"8"}},t._l(t.collections,(function(a){return e("u-col",{attrs:{span:"6"}},[e("v-uni-view",{staticClass:"collection"},[e("v-uni-view",{staticClass:"collection-content",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.holdCollectionDetailPage(a.id)}}},[e("v-uni-image",{staticClass:"collection-cover",staticStyle:{height:"280rpx",width:"100%"},attrs:{src:a.collectionCover}}),e("v-uni-view",{staticClass:"collection-name u-line-1"},[t._v(t._s(a.collectionName))]),e("v-uni-view",{staticClass:"collection-hold-date"},[t._v("收藏于 "+t._s(a.holdDate))])],1)],1)],1)})),1)],1),e("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1==t.currentTab,expression:"currentTab == 1"}]},[e("u-row",{attrs:{gutter:"8"}},t._l(t.mysteryBoxs,(function(a){return e("u-col",{attrs:{span:"6"}},[e("v-uni-view",{staticClass:"collection"},[e("v-uni-view",{staticClass:"collection-content",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.holdCollectionDetailPage(a.id)}}},[e("v-uni-image",{staticClass:"collection-cover",staticStyle:{height:"280rpx",width:"100%"},attrs:{src:a.collectionCover}}),e("v-uni-view",{staticClass:"collection-name u-line-1"},[t._v(t._s(a.collectionName))]),e("v-uni-view",{staticClass:"collection-hold-date"},[t._v("收藏于 "+t._s(a.holdDate))])],1)],1)],1)})),1)],1),e("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:2==t.currentTab,expression:"currentTab == 2"}]},[e("u-row",{attrs:{gutter:"8"}},t._l(t.soldCollections,(function(a){return e("u-col",{attrs:{span:"6"}},[e("v-uni-view",{staticClass:"collection"},[e("v-uni-view",{staticClass:"collection-content"},[e("v-uni-image",{staticClass:"collection-cover",staticStyle:{height:"280rpx",width:"100%"},attrs:{src:a.collectionCover}}),e("v-uni-view",{staticClass:"collection-name u-line-1"},[t._v(t._s(a.collectionName))]),e("v-uni-view",{staticClass:"collection-hold-date"},[t._v("卖出于 "+t._s(a.soldDate))]),e("v-uni-view",{staticClass:"resale-price"},[t._v("￥"+t._s(a.soldPrice))])],1)],1)],1)})),1)],1),e("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:3==t.currentTab,expression:"currentTab == 3"}]},[e("u-row",{attrs:{gutter:"8"}},t._l(t.resaleCollections,(function(a){return e("u-col",{attrs:{span:"6"}},[e("v-uni-view",{staticClass:"collection"},[e("v-uni-view",{staticClass:"collection-content",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.myResaleCollectionDetailPage(a.id)}}},[e("v-uni-image",{staticClass:"collection-cover",staticStyle:{height:"280rpx",width:"100%"},attrs:{src:a.collectionCover}}),e("v-uni-view",{staticClass:"collection-name u-line-1"},[t._v(t._s(a.collectionName))]),e("v-uni-view",{staticClass:"collection-hold-date"},[t._v("发布于 "+t._s(a.resaleDate))]),e("v-uni-view",{staticClass:"resale-price"},[t._v("￥"+t._s(a.resalePrice))])],1)],1)],1)})),1)],1)],1),e("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!t.noDataFlag,expression:"!noDataFlag"}],on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.nextPage.apply(void 0,arguments)}}},[e("u-loadmore",{attrs:{"margin-top":"40","margin-bottom":"40",status:t.loadingState}})],1)],1)],1)],1)},o=[]},"9cb2":function(t,a,e){var n=e("24fb");a=n(!1),a.push([t.i,"uni-page-body[data-v-7b63e37c]{height:100%!important}.msg-unread-num[data-v-7b63e37c]{background-color:#d10e0e}.setting[data-v-7b63e37c]{color:#fff;padding-right:%?32?%;position:relative;top:%?-40?%}.msg-read[data-v-7b63e37c]{height:%?24?%;width:%?24?%;border-radius:%?24?%;line-height:%?24?%;text-align:center;font-size:%?20?%;-webkit-transform:scale(.8);transform:scale(.8);position:absolute;top:%?0?%;left:%?46?%}.div-line[data-v-7b63e37c]{width:100%;height:%?20?%;background:#f0f0f0}.resale-price[data-v-7b63e37c]{padding-left:%?16?%}.custom-tabs[data-v-7b63e37c]{padding-bottom:%?32?%;display:flex;align-items:center}.custom-tabs-l[data-v-7b63e37c]{flex:1}.compose-activity[data-v-7b63e37c]{background:linear-gradient(90deg,#2979ff,#909399);height:%?56?%;line-height:%?56?%;border-radius:%?44?% %?0?% %?0?% %?44?%;font-size:small;color:#fff;padding-left:%?24?%;padding-right:%?24?%;margin-left:%?12?%}.custom-tab-content[data-v-7b63e37c]{padding-left:%?32?%;padding-right:%?32?%}.collection[data-v-7b63e37c]{padding-bottom:%?10?%}.collection-content[data-v-7b63e37c]{background:#e7e7e7;border-radius:%?20?%;padding-bottom:%?20?%}.collection-cover[data-v-7b63e37c]{border-radius:%?20?% %?20?% %?0?% %?0?%}.collection-name[data-v-7b63e37c]{font-size:%?26?%;padding-left:%?16?%;padding-top:%?16?%;color:#000}.collection-hold-date[data-v-7b63e37c]{font-size:%?24?%;padding-left:%?16?%}.no-data[data-v-7b63e37c]{display:flex;align-items:center;justify-content:center;height:%?400?%}.grid-navs[data-v-7b63e37c]{display:flex;justify-content:space-around;align-items:center}.grid-nav[data-v-7b63e37c]{color:#fff;text-align:center;padding-top:%?20?%;padding-bottom:%?20?%;position:relative}.page-content[data-v-7b63e37c]{padding-bottom:%?140?%}.top-content[data-v-7b63e37c]{padding-top:%?64?%;background:linear-gradient(90deg,#2f2f2f,#747171)}.avatar-nick-name[data-v-7b63e37c]{display:flex;align-items:center;padding-bottom:%?16?%;padding-left:%?32?%}.nick-name-mobile[data-v-7b63e37c]{flex:1}.login-t[data-v-7b63e37c]{padding-bottom:%?12?%;color:#fff}.login-b[data-v-7b63e37c]{color:#888;font-size:smaller}.member-avatar[data-v-7b63e37c]{padding-right:%?20?%}.member-nick-name[data-v-7b63e37c]{font-size:larger;padding-bottom:%?12?%;color:#fff}.member-mobile[data-v-7b63e37c]{font-size:small;color:#888}.block-chain-addr[data-v-7b63e37c]{display:flex;align-items:center;justify-content:space-between;padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?16?%}.block-chain-addr-l[data-v-7b63e37c]{color:#fff;font-size:small;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;padding-right:%?60?%}",""]),t.exports=a},b54f:function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var n={data:function(){return{personalInfo:"",tabs:[{name:"藏品"},{name:"盲盒"},{name:"已卖出"},{name:"已发布"}],currentTab:0,collections:[],mysteryBoxs:[],resaleCollections:[],soldCollections:[],pageNum:1,loadingState:"loadmore",pullDownRefreshFlag:!1,noDataFlag:!1,unreadMsgIds:[],customerServiceUrl:""}},onLoad:function(t){var a=t.tab;a&&(this.currentTab=a)},onShow:function(){this.getCustomerServiceUrl(),this.getMyPersonalInfo(),this.findByPage()},onReachBottom:function(){this.nextPage()},onPullDownRefresh:function(){this.pullDownRefreshFlag=!0,this.refreshData()},methods:{loginPage:function(){uni.reLaunch({url:"../login/login"})},isLogin:function(){var t=uni.getStorageSync("tokenName");return null!=t&&""!=t},getBlockChainAddr:function(){if(this.personalInfo)return null===this.personalInfo.bindRealNameTime||""===this.personalInfo.bindRealNameTime?"实名认证后生成区块链地址...":this.personalInfo.blockChainAddr?this.personalInfo.blockChainAddr:"等待创建区块链地址..."},getCustomerServiceUrl:function(){var t=this;this.$u.get("/setting/getCustomerServiceUrl").then((function(a){t.customerServiceUrl=a.data}))},contactCustomerService:function(){this.customerServiceUrl?uni.navigateTo({url:"../customerService/customerService?customerServiceUrl="+this.customerServiceUrl}):uni.showToast({title:"客服太繁忙了",icon:"none"})},switchTab:function(t){this.currentTab=t,this.refreshData()},copyData:function(t){uni.setClipboardData({data:""+t,success:function(){uni.showToast({title:"复制成功",duration:2e3,icon:"none"})},fail:function(t){uni.showToast({title:"复制失败"+t,duration:2e3,icon:"none"})}})},myResaleCollectionDetailPage:function(t){uni.navigateTo({url:"../myResaleCollectionDetail/myResaleCollectionDetail?id="+t})},holdCollectionDetailPage:function(t){uni.navigateTo({url:"../holdCollectionDetail/holdCollectionDetail?id="+t})},refreshData:function(){this.pageNum=1,this.loadingState="loading",this.findByPage()},nextPage:function(){"nomore"!=this.loadingState&&(this.pageNum=this.pageNum+1,this.findByPage())},findByPage:function(){this.isLogin()?0==this.currentTab?this.findMyHoldCollectionByPage():1==this.currentTab?this.findMyHoldMysteryBoxByPage():2==this.currentTab?this.findMySoldCollectionByPage():3==this.currentTab&&this.findMyResaleCollectionByPage():this.noDataFlag=!0},findMyResaleCollectionByPage:function(){var t=this;1==t.pageNum&&(t.resaleCollections=[]);var a={pageSize:10,pageNum:t.pageNum};t.loadingState="loading",this.$u.get("/myArtwork/findMyResaleCollectionByPage",a).then((function(a){var e=a.data.content,n=a.data.totalPage;t.pullDownRefreshFlag&&(t.pullDownRefreshFlag=!1,uni.stopPullDownRefresh()),0==e.length&&(t.loadingState="nomore"),n==t.pageNum&&(t.loadingState="nomore");for(var i=t.resaleCollections,o=0;o<e.length;o++){for(var s=!0,l=0;l<i.length;l++)if(e[o].id==i[l].id){s=!1;break}s&&i.push(e[o])}t.noDataFlag=0==i.length}))},findMyHoldMysteryBoxByPage:function(){var t=this;1==t.pageNum&&(t.mysteryBoxs=[]);var a={pageSize:10,pageNum:t.pageNum};t.loadingState="loading",this.$u.get("/myArtwork/findMyHoldMysteryBoxByPage",a).then((function(a){var e=a.data.content,n=a.data.totalPage;t.pullDownRefreshFlag&&(t.pullDownRefreshFlag=!1,uni.stopPullDownRefresh()),0==e.length&&(t.loadingState="nomore"),n==t.pageNum&&(t.loadingState="nomore");for(var i=t.mysteryBoxs,o=0;o<e.length;o++){for(var s=!0,l=0;l<i.length;l++)if(e[o].id==i[l].id){s=!1;break}s&&i.push(e[o])}t.noDataFlag=0==i.length}))},findMyHoldCollectionByPage:function(){var t=this;1==t.pageNum&&(t.collections=[]);var a={pageSize:10,pageNum:t.pageNum};t.loadingState="loading",this.$u.get("/myArtwork/findMyHoldCollectionByPage",a).then((function(a){var e=a.data.content,n=a.data.totalPage;t.pullDownRefreshFlag&&(t.pullDownRefreshFlag=!1,uni.stopPullDownRefresh()),0==e.length&&(t.loadingState="nomore"),n==t.pageNum&&(t.loadingState="nomore");for(var i=t.collections,o=0;o<e.length;o++){for(var s=!0,l=0;l<i.length;l++)if(e[o].id==i[l].id){s=!1;break}s&&i.push(e[o])}t.noDataFlag=0==i.length}))},findMySoldCollectionByPage:function(){var t=this;1==t.pageNum&&(t.soldCollections=[]);var a={pageSize:10,pageNum:t.pageNum};t.loadingState="loading",this.$u.get("/myArtwork/findMySoldCollectionByPage",a).then((function(a){var e=a.data.content,n=a.data.totalPage;t.pullDownRefreshFlag&&(t.pullDownRefreshFlag=!1,uni.stopPullDownRefresh()),0==e.length&&(t.loadingState="nomore"),n==t.pageNum&&(t.loadingState="nomore");for(var i=t.soldCollections,o=0;o<e.length;o++){for(var s=!0,l=0;l<i.length;l++)if(e[o].id==i[l].id){s=!1;break}s&&i.push(e[o])}t.noDataFlag=0==i.length}))},gotoPage:function(t){this.isLogin()?uni.navigateTo({url:t}):this.loginPage()},getAvatar:function(){return this.personalInfo.avatar?this.baseUrl+"/storage/fetch/"+this.personalInfo.avatar:"/static/img/avatar.png"},getMyPersonalInfo:function(){if(this.isLogin()){var t=this;this.$u.get("/member/getMyPersonalInfo").then((function(a){t.personalInfo=a.data}))}}}};a.default=n},bf63:function(t,a,e){var n=e("8de2");"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=e("4f06").default;i("02d2baee",n,!0,{sourceMap:!1,shadowMode:!1})},c08e:function(t,a,e){"use strict";var n=e("94b7"),i=e.n(n);i.a},f225:function(t,a,e){"use strict";e.r(a);var n=e("0011"),i=e.n(n);for(var o in n)"default"!==o&&function(t){e.d(a,t,(function(){return n[t]}))}(o);a["default"]=i.a}}]);