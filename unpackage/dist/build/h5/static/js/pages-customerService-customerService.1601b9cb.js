(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-customerService-customerService"],{3875:function(e,t,r){"use strict";var n;r.d(t,"b",(function(){return u})),r.d(t,"c",(function(){return c})),r.d(t,"a",(function(){return n}));var u=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-uni-view",[r("v-uni-web-view",{attrs:{src:e.customerServiceUrl}})],1)},c=[]},8186:function(e,t,r){"use strict";r.r(t);var n=r("3875"),u=r("d404");for(var c in u)"default"!==c&&function(e){r.d(t,e,(function(){return u[e]}))}(c);var i,o=r("f0c5"),a=Object(o["a"])(u["default"],n["b"],n["c"],!1,null,"021d4c3f",null,!1,n["a"],i);t["default"]=a.exports},8651:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={data:function(){return{customerServiceUrl:""}},onLoad:function(e){this.customerServiceUrl=e.customerServiceUrl},methods:{}};t.default=n},d404:function(e,t,r){"use strict";r.r(t);var n=r("8651"),u=r.n(n);for(var c in n)"default"!==c&&function(e){r.d(t,e,(function(){return n[e]}))}(c);t["default"]=u.a}}]);