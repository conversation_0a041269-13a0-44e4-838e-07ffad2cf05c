(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-composeCollection-composeCollection"],{"0011":function(t,e,a){"use strict";a("a9e3"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"u-row",props:{gutter:{type:[String,Number],default:20},justify:{type:String,default:"start"},align:{type:String,default:"center"},stop:{type:Boolean,default:!0}},computed:{uJustify:function(){return"end"==this.justify||"start"==this.justify?"flex-"+this.justify:"around"==this.justify||"between"==this.justify?"space-"+this.justify:this.justify},uAlignItem:function(){return"top"==this.align?"flex-start":"bottom"==this.align?"flex-end":this.align}},methods:{click:function(t){this.$emit("click")}}};e.default=i},"099a":function(t,e,a){"use strict";var i;a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+t.size,t.plain?"u-btn--"+t.type+"--plain":"",t.loading?"u-loading":"","circle"==t.shape?"u-round-circle":"",t.hairLine?t.showHairLineBorder:"u-btn--bold-border","u-btn--"+t.type,t.disabled?"u-btn--"+t.type+"--disabled":""],style:[t.customStyle,{overflow:t.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),disabled:t.disabled,"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":"sendMessagePath",lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.getHoverClass,loading:t.loading},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.getuserinfo.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.opensetting.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.launchapp.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click(e)}}},[t._t("default"),t.ripple?a("v-uni-view",{staticClass:"u-wave-ripple",class:[t.waveActive?"u-wave-active":""],style:{top:t.rippleTop+"px",left:t.rippleLeft+"px",width:t.fields.targetWidth+"px",height:t.fields.targetWidth+"px","background-color":t.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):t._e()],2)},o=[]},"0ef0":function(t,e,a){"use strict";a.r(e);var i=a("a26f"),n=a.n(i);for(var o in i)"default"!==o&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"10ab":function(t,e,a){"use strict";var i;a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"u-col",class:["u-col-"+t.span],style:{padding:"0 "+Number(t.gutter)/2+"rpx",marginLeft:100/12*t.offset+"%",flex:"0 0 "+100/12*t.span+"%",alignItems:t.uAlignItem,justifyContent:t.uJustify,textAlign:t.textAlign},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.click.apply(void 0,arguments)}}},[t._t("default")],2)},o=[]},1530:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-e356a272]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-e356a272]{text-align:center}.pay-modal-amount-value[data-v-e356a272]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-e356a272]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-e356a272]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-e356a272]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-e356a272]{font-size:%?36?%}.custom-pay-modal-btn[data-v-e356a272]{width:80%}.u-checkbox[data-v-e356a272]{display:inline-flex;align-items:center;overflow:hidden;-webkit-user-select:none;user-select:none;line-height:1.8}.u-checkbox__icon-wrap[data-v-e356a272]{color:#606266;flex:none;display:-webkit-flex;display:flex;flex-direction:row;align-items:center;justify-content:center;box-sizing:border-box;width:%?42?%;height:%?42?%;color:transparent;text-align:center;transition-property:color,border-color,background-color;font-size:20px;border:1px solid #c8c9cc;transition-duration:.2s}.u-checkbox__icon-wrap--circle[data-v-e356a272]{border-radius:100%}.u-checkbox__icon-wrap--square[data-v-e356a272]{border-radius:%?6?%}.u-checkbox__icon-wrap--checked[data-v-e356a272]{color:#fff;background-color:#2979ff;border-color:#2979ff}.u-checkbox__icon-wrap--disabled[data-v-e356a272]{background-color:#ebedf0;border-color:#c8c9cc}.u-checkbox__icon-wrap--disabled--checked[data-v-e356a272]{color:#c8c9cc!important}.u-checkbox__label[data-v-e356a272]{word-wrap:break-word;margin-left:%?10?%;margin-right:%?24?%;color:#606266;font-size:%?30?%}.u-checkbox__label--disabled[data-v-e356a272]{color:#c8c9cc}',""]),t.exports=e},2369:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={uModal:a("4e29").default,uPopup:a("b7ad").default,uIcon:a("8ed9").default,uCheckbox:a("eb84").default,uButton:a("2e56").default,uRow:a("3630").default,uCol:a("46f4").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"page-content"},[a("u-modal",{attrs:{title:"提示","show-cancel-button":!0,"cancel-text":"取消","confirm-text":"确定"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.compose.apply(void 0,arguments)}},model:{value:t.composeConfirmFlag,callback:function(e){t.composeConfirmFlag=e},expression:"composeConfirmFlag"}},[a("v-uni-view",{staticClass:"slot-content"},[a("v-uni-view",{staticClass:"compose-confirm-content"},[t._v("您选择了"+t._s(t.calcRequireMaterialQuantity())+'个藏品参与"'+t._s(t.activityDetail.title)+'"活动，一旦合成，该批藏品将进行销毁，是否确认合成？')])],1)],1),a("u-modal",{attrs:{"show-title":!1,"show-cancel-button":!1,"confirm-text":"我知道了"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.myPage.apply(void 0,arguments)}},model:{value:t.composeResultFlag,callback:function(e){t.composeResultFlag=e},expression:"composeResultFlag"}},[a("v-uni-view",{staticClass:"slot-content"},[a("v-uni-view",{staticClass:"compose-result-content"},[a("v-uni-view",{staticClass:"compose-result-title"},[t._v("恭喜获得")]),a("v-uni-view",{staticClass:"compose-result-name"},[t._v(t._s(t.activityDetail.collectionName))]),a("v-uni-image",{staticStyle:{width:"200rpx",height:"200rpx"},attrs:{src:t.activityDetail.collectionCover}})],1)],1)],1),a("u-modal",{attrs:{title:"温馨提示","show-cancel-button":!0,"cancel-text":"取消","confirm-text":"去购买",content:"你没有当前藏品，是否去购买"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.marketPage.apply(void 0,arguments)}},model:{value:t.noAvailableMaterialFlag,callback:function(e){t.noAvailableMaterialFlag=e},expression:"noAvailableMaterialFlag"}}),a("u-popup",{attrs:{mode:"center",width:"80%"},model:{value:t.composeRequireFlag,callback:function(e){t.composeRequireFlag=e},expression:"composeRequireFlag"}},[a("v-uni-view",{staticClass:"compose-require"},[a("v-uni-view",{staticClass:"compose-require-title"},[a("v-uni-text",[t._v("合成规则")]),a("u-icon",{staticClass:"compose-require-title-close",attrs:{name:"close"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.composeRequireFlag=!1}}})],1),a("v-uni-view",{staticClass:"compose-require-content"},t._l(t.materials,(function(e,i){return a("v-uni-view",{staticClass:"compose-require-item"},[t._v(t._s(e.materialName)+"*"+t._s(e.quantity))])})),1)],1)],1),a("u-popup",{attrs:{mode:"bottom","border-radius":"14"},model:{value:t.selectMaterialModalFlag,callback:function(e){t.selectMaterialModalFlag=e},expression:"selectMaterialModalFlag"}},[a("v-uni-view",{staticClass:"common-modal"},[a("v-uni-view",{staticClass:"modal-title"},[a("v-uni-view",[a("u-icon",{attrs:{name:"arrow-leftward"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.hideSelectMaterialModal.apply(void 0,arguments)}}})],1),a("v-uni-view",{staticClass:"modal-title-txt"},[t._v("选择原料")]),a("v-uni-view",{staticClass:"close-buy-txt",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.hideSelectMaterialModal.apply(void 0,arguments)}}},[t._v("关闭")])],1),a("v-uni-view",[a("v-uni-view",{staticClass:"select-material-name"},[t._v(t._s(t.selectMaterial.materialName))]),t._l(t.copayMyHolds,(function(e){return a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.collectionId==t.selectMaterial.materialId,expression:"myHold.collectionId == selectMaterial.materialId"}],staticClass:"optional-material",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),e.selectedFlag=!e.selectedFlag}}},[a("v-uni-view",{staticClass:"optional-material-id"},[t._v("#"+t._s(e.collectionSerialNumber))]),a("v-uni-view",[a("u-checkbox",{directives:[{name:"show",rawName:"v-show",value:e.selectedFlag,expression:"myHold.selectedFlag"}],attrs:{shape:"circle",disabled:!0},model:{value:t.checkboxTrue,callback:function(e){t.checkboxTrue=e},expression:"checkboxTrue"}}),a("u-checkbox",{directives:[{name:"show",rawName:"v-show",value:!e.selectedFlag,expression:"!myHold.selectedFlag"}],attrs:{shape:"circle",disabled:!0},model:{value:t.checkboxFalse,callback:function(e){t.checkboxFalse=e},expression:"checkboxFalse"}})],1)],1)})),a("u-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmSelected.apply(void 0,arguments)}}},[t._v("确定(已选"+t._s(t.calcSelectedMaterialQuantity(t.selectMaterial.materialId,t.copayMyHolds))+"个)")])],2)],1)],1),t._l(t.materials,(function(e,i){return a("v-uni-view",[a("v-uni-view",{staticClass:"condition"},[a("v-uni-view",{staticClass:"condition-title"},[a("v-uni-view",{staticClass:"condition-title-l"},[t._v("条件"+t._s(i+1)+" （请选择"+t._s(e.quantity)+"个藏品）:")]),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.calcSelectedMaterialQuantity(e.materialId,t.myHolds)>0,expression:"calcSelectedMaterialQuantity(material.materialId, myHolds) > 0"}],staticClass:"condition-title-r"},[a("u-button",{attrs:{type:"primary",size:"mini",plain:!0},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.showSelectMaterialModal(e)}}},[t._v("重新选择")])],1)],1),a("u-row",{attrs:{gutter:"8"}},[a("u-col",{directives:[{name:"show",rawName:"v-show",value:0==t.calcSelectedMaterialQuantity(e.materialId,t.myHolds),expression:"calcSelectedMaterialQuantity(material.materialId, myHolds) == 0"}],attrs:{span:"4"}},[a("v-uni-view",{staticClass:"require-material"},[a("v-uni-view",{staticClass:"require-material-cover"},[a("v-uni-image",{staticClass:"require-material-cover-img",staticStyle:{height:"200rpx",width:"100%",opacity:"0.4"},attrs:{src:e.materialCover}}),a("v-uni-view",{staticClass:"require-material-cover-add",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.showSelectMaterialModal(e)}}},[a("u-icon",{attrs:{name:"plus",color:"white",size:"60"}})],1)],1),a("v-uni-view",{staticClass:"require-material-name u-line-1"},[t._v(t._s(e.materialName))])],1)],1),t._l(t.filterSelectedMaterial(e.materialId),(function(e,i){return a("u-col",{attrs:{span:"4"}},[a("v-uni-view",{staticClass:"selected-material"},[a("v-uni-view",{staticClass:"selected-material-cover"},[a("v-uni-image",{staticClass:"selected-material-cover-img",staticStyle:{height:"200rpx",width:"100%"},attrs:{src:e.collectionCover}}),a("v-uni-view",{staticClass:"selected-material-num"},[t._v("原料"+t._s(i+1))])],1),a("v-uni-view",{staticClass:"selected-material-name u-line-1"},[t._v(t._s(e.collectionName))]),a("v-uni-view",{staticClass:"selected-material-id"},[a("v-uni-text",{staticClass:"selected-material-id-l"},[t._v("#"+t._s(e.collectionSerialNumber))]),a("v-uni-text",[t._v("/"+t._s(e.quantity))])],1)],1)],1)}))],2)],1),a("v-uni-view",{staticClass:"div-line"})],1)})),a("v-uni-view",{staticClass:"compose-after"},[a("v-uni-image",{staticClass:"compose-arrow",staticStyle:{height:"60rpx",width:"60rpx"},attrs:{src:"/static/img/arrow-down.png"}}),a("v-uni-view",{staticClass:"compose-after-collection"},[a("v-uni-image",{staticClass:"compose-after-collection-img",staticStyle:{height:"240rpx",width:"240rpx"},attrs:{src:t.activityDetail.collectionCover}}),a("v-uni-view",{staticClass:"compose-after-collection-name"},[t._v(t._s(t.activityDetail.collectionName))])],1)],1),a("v-uni-view",{staticClass:"fixed-bottom"},[a("v-uni-view",{staticClass:"fixed-bottom-content"},[a("v-uni-view",{staticClass:"fixed-bottom-content-inner"},[a("v-uni-view",{staticClass:"fixed-bottom-content-inner-l",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.marketPage.apply(void 0,arguments)}}},[a("v-uni-image",{staticStyle:{width:"40rpx",height:"40rpx"},attrs:{src:"/static/img/market.png"}}),a("v-uni-view",[t._v("去市场")])],1),a("v-uni-view",{staticClass:"fixed-bottom-content-inner-r"},[a("v-uni-view",{staticClass:"fixed-bottom-tip"},[t._v("集齐以上藏品即可进行合成")]),a("v-uni-view",{staticClass:"compose-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.composeStep1.apply(void 0,arguments)}}},[t._v("立即合成")])],1)],1)],1)],1)],2)},o=[]},"298a":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,"[data-v-6ad4a6db] .u-checkbox__icon-wrap--disabled{color:transparent!important;background-color:initial!important;border-color:#c8c9cc!important}[data-v-6ad4a6db] .u-checkbox__icon-wrap--disabled--checked{color:#fff!important;background-color:#2979ff!important;border-color:#2979ff!important}.page-content[data-v-6ad4a6db]{padding-bottom:%?280?%}.compose-require[data-v-6ad4a6db]{padding:%?24?%}.compose-require-title[data-v-6ad4a6db]{text-align:center}.compose-require-title-close[data-v-6ad4a6db]{float:right}.compose-require-item[data-v-6ad4a6db]{color:#000;padding-top:%?4?%;padding-bottom:%?4?%}.compose-after[data-v-6ad4a6db]{text-align:center}.div-line[data-v-6ad4a6db]{width:100%;height:%?20?%;background:#f3f3f3}.compose-after-collection[data-v-6ad4a6db]{padding-top:%?16?%}.compose-result-content[data-v-6ad4a6db]{text-align:center;padding-top:20px;padding-bottom:20px}.compose-result-name[data-v-6ad4a6db]{line-height:2;color:#888}.compose-result-title[data-v-6ad4a6db]{line-height:2;font-size:larger}.compose-confirm-content[data-v-6ad4a6db]{font-size:smaller;padding:%?32?%}.select-material-name[data-v-6ad4a6db]{padding-top:%?32?%;padding-bottom:%?32?%}.optional-material[data-v-6ad4a6db]{display:flex;justify-content:space-between;align-items:center;line-height:3;color:#888}.common-modal[data-v-6ad4a6db]{padding-left:%?32?%;padding-right:%?32?%;padding-top:%?32?%;padding-bottom:%?32?%}.modal-title[data-v-6ad4a6db]{display:flex;justify-content:space-between}.modal-title-txt[data-v-6ad4a6db]{font-weight:700}.close-modal-txt[data-v-6ad4a6db]{color:#909399}.fixed-bottom-content-inner[data-v-6ad4a6db]{display:flex;align-items:center}.fixed-bottom-content-inner-l[data-v-6ad4a6db]{text-align:center}.fixed-bottom-content-inner-r[data-v-6ad4a6db]{text-align:center;flex:1;padding-left:%?64?%}.fixed-bottom-tip[data-v-6ad4a6db]{color:#888;padding-bottom:%?32?%}.compose-btn[data-v-6ad4a6db]{background:#2979ff;font-size:large;font-weight:700;color:#fff;padding:%?20?% %?60?%}.fixed-bottom-content[data-v-6ad4a6db]{padding-top:%?32?%;padding-bottom:%?32?%}.fixed-bottom[data-v-6ad4a6db]{position:fixed;bottom:%?0?%;left:%?0?%;width:100%;background:#fff;padding-left:%?32?%;padding-right:%?32?%}.condition[data-v-6ad4a6db]{background:#fff;padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?8?%}.condition-title[data-v-6ad4a6db]{display:flex;justify-content:space-between;align-items:center}.condition-title-l[data-v-6ad4a6db]{color:#888;line-height:3;font-size:smaller}.selected-material[data-v-6ad4a6db]{padding-bottom:%?8?%}.selected-material-cover[data-v-6ad4a6db]{position:relative}.selected-material-num[data-v-6ad4a6db]{position:absolute;top:0;right:0;bottom:0;left:0;display:flex;flex-direction:column;align-items:center;justify-content:center;background-color:hsl(0deg 0% 100%/22%);height:%?40?%;width:%?80?%;font-size:smaller;color:#ecf5ff}.selected-material-name[data-v-6ad4a6db]{font-size:small;padding-left:%?4?%}.selected-material-id[data-v-6ad4a6db]{font-size:small;padding-left:%?4?%}.selected-material-id-l[data-v-6ad4a6db]{color:#888}.require-material-cover[data-v-6ad4a6db]{position:relative}.require-material-cover-add[data-v-6ad4a6db]{position:absolute;top:0;right:0;bottom:0;left:0;display:flex;flex-direction:column;align-items:center;justify-content:center;background-color:rgb(0 0 0/17%);height:%?200?%}.require-material-name[data-v-6ad4a6db]{font-size:small;padding-left:%?4?%}uni-page-body[data-v-6ad4a6db]{height:100%!important;background-color:#f3f3f3}body.?%PAGE?%[data-v-6ad4a6db]{background-color:#f3f3f3}",""]),t.exports=e},"29d4":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-0a5981a4]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-0a5981a4]{text-align:center}.pay-modal-amount-value[data-v-0a5981a4]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-0a5981a4]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-0a5981a4]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-0a5981a4]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-0a5981a4]{font-size:%?36?%}.custom-pay-modal-btn[data-v-0a5981a4]{width:80%}.u-col-0[data-v-0a5981a4]{width:0}.u-col-1[data-v-0a5981a4]{width:calc(100%/12)}.u-col-2[data-v-0a5981a4]{width:calc(100%/12 * 2)}.u-col-3[data-v-0a5981a4]{width:calc(100%/12 * 3)}.u-col-4[data-v-0a5981a4]{width:calc(100%/12 * 4)}.u-col-5[data-v-0a5981a4]{width:calc(100%/12 * 5)}.u-col-6[data-v-0a5981a4]{width:calc(100%/12 * 6)}.u-col-7[data-v-0a5981a4]{width:calc(100%/12 * 7)}.u-col-8[data-v-0a5981a4]{width:calc(100%/12 * 8)}.u-col-9[data-v-0a5981a4]{width:calc(100%/12 * 9)}.u-col-10[data-v-0a5981a4]{width:calc(100%/12 * 10)}.u-col-11[data-v-0a5981a4]{width:calc(100%/12 * 11)}.u-col-12[data-v-0a5981a4]{width:calc(100%/12 * 12)}',""]),t.exports=e},"2e49":function(t,e,a){var i=a("29d4");"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("cf69df92",i,!0,{sourceMap:!1,shadowMode:!1})},"2e56":function(t,e,a){"use strict";a.r(e);var i=a("099a"),n=a("f1fb");for(var o in n)"default"!==o&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("ee7b");var r,l=a("f0c5"),c=Object(l["a"])(n["default"],i["b"],i["c"],!1,null,"23f00fb2",null,!1,i["a"],r);e["default"]=c.exports},"32d0":function(t,e,a){"use strict";a.r(e);var i=a("2369"),n=a("9bf8");for(var o in n)"default"!==o&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("a247");var r,l=a("f0c5"),c=Object(l["a"])(n["default"],i["b"],i["c"],!1,null,"6ad4a6db",null,!1,i["a"],r);e["default"]=c.exports},"35c9":function(t,e,a){"use strict";var i=a("bf63"),n=a.n(i);n.a},3630:function(t,e,a){"use strict";a.r(e);var i=a("8ee4"),n=a("f225");for(var o in n)"default"!==o&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("35c9");var r,l=a("f0c5"),c=Object(l["a"])(n["default"],i["b"],i["c"],!1,null,"345affc4",null,!1,i["a"],r);e["default"]=c.exports},"3f20":function(t,e,a){"use strict";var i=a("2e49"),n=a.n(i);n.a},"46f4":function(t,e,a){"use strict";a.r(e);var i=a("10ab"),n=a("7131");for(var o in n)"default"!==o&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("3f20");var r,l=a("f0c5"),c=Object(l["a"])(n["default"],i["b"],i["c"],!1,null,"0a5981a4",null,!1,i["a"],r);e["default"]=c.exports},"4c99":function(t,e,a){"use strict";a("a9e3"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"u-col",props:{span:{type:[Number,String],default:12},offset:{type:[Number,String],default:0},justify:{type:String,default:"start"},align:{type:String,default:"center"},textAlign:{type:String,default:"left"},stop:{type:Boolean,default:!0}},data:function(){return{gutter:20}},created:function(){this.parent=!1},mounted:function(){this.parent=this.$u.$parent.call(this,"u-row"),this.parent&&(this.gutter=this.parent.gutter)},computed:{uJustify:function(){return"end"==this.justify||"start"==this.justify?"flex-"+this.justify:"around"==this.justify||"between"==this.justify?"space-"+this.justify:this.justify},uAlignItem:function(){return"top"==this.align?"flex-start":"bottom"==this.align?"flex-end":this.align}},methods:{click:function(t){this.$emit("click")}}};e.default=i},"52a3":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-23f00fb2]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-23f00fb2]{text-align:center}.pay-modal-amount-value[data-v-23f00fb2]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-23f00fb2]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-23f00fb2]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-23f00fb2]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-23f00fb2]{font-size:%?36?%}.custom-pay-modal-btn[data-v-23f00fb2]{width:80%}.u-btn[data-v-23f00fb2]::after{border:none}.u-btn[data-v-23f00fb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-23f00fb2]{border:1px solid #fff}.u-btn--default[data-v-23f00fb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-23f00fb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-23f00fb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-23f00fb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-23f00fb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-23f00fb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-23f00fb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-23f00fb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-23f00fb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-23f00fb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-23f00fb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-23f00fb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-23f00fb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-23f00fb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-23f00fb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-23f00fb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-23f00fb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-23f00fb2]{border-radius:%?100?%}.u-round-circle[data-v-23f00fb2]::after{border-radius:%?100?%}.u-loading[data-v-23f00fb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-23f00fb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-23f00fb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-23f00fb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-23f00fb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-23f00fb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-23f00fb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-23f00fb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-23f00fb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-23f00fb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-23f00fb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-23f00fb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-23f00fb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-23f00fb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-23f00fb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-23f00fb2]{background:#dd6161!important;color:#fff}',""]),t.exports=e},7131:function(t,e,a){"use strict";a.r(e);var i=a("4c99"),n=a.n(i);for(var o in i)"default"!==o&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"8de2":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-345affc4]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-345affc4]{text-align:center}.pay-modal-amount-value[data-v-345affc4]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-345affc4]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-345affc4]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-345affc4]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-345affc4]{font-size:%?36?%}.custom-pay-modal-btn[data-v-345affc4]{width:80%}.u-row[data-v-345affc4]{display:flex;flex-direction:row;flex-wrap:wrap}',""]),t.exports=e},"8ee4":function(t,e,a){"use strict";var i;a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"u-row",style:{alignItems:t.uAlignItem,justifyContent:t.uJustify},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.click.apply(void 0,arguments)}}},[t._t("default")],2)},o=[]},9675:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={data:function(){return{id:"",materials:[],activityDetail:"",myHolds:[],copayMyHolds:[],checkboxTrue:!0,checkboxFalse:!1,selectMaterialModalFlag:!1,selectMaterial:"",noAvailableMaterialFlag:!1,composeConfirmFlag:!1,composeResultFlag:!1,composeRequireFlag:!1}},onNavigationBarButtonTap:function(t){this.composeRequireFlag=!0},onLoad:function(t){this.id=t.id,this.findActiveComposeActivityDetail(),this.findAllMyHold()},methods:{myPage:function(){uni.reLaunch({url:"../my/my"})},marketPage:function(){uni.reLaunch({url:"../market/market"})},calcRequireMaterialQuantity:function(){for(var t=0,e=0;e<this.materials.length;e++)t+=this.materials[e].quantity;return t},compose:function(){for(var t=this,e=[],a=0;a<this.myHolds.length;a++)this.myHolds[a].selectedFlag&&e.push(this.myHolds[a].id);this.$u.post("/composeActivity/compose",{activityId:t.id,holdCollectionIds:e}).then((function(e){t.composeResultFlag=!0}))},composeStep1:function(){for(var t=this,e=0;e<t.materials.length;e++){var a=t.materials[e],i=t.calcSelectedMaterialQuantity(a.materialId,t.myHolds);if(i!=a.quantity)return void uni.showToast({title:"原料数量不正确",icon:"none"})}t.composeConfirmFlag=!0},filterSelectedMaterial:function(t){if(null===t||""===t)return[];for(var e=[],a=0;a<this.myHolds.length;a++)t==this.myHolds[a].collectionId&&this.myHolds[a].selectedFlag&&e.push(this.myHolds[a]);return e},calcAvailableMaterialQuantity:function(t){if(null===t||""===t)return 0;for(var e=0,a=0;a<this.myHolds.length;a++)t==this.myHolds[a].collectionId&&e++;return e},calcSelectedMaterialQuantity:function(t,e){if(null===t||""===t)return 0;for(var a=0,i=0;i<e.length;i++)t==e[i].collectionId&&e[i].selectedFlag&&a++;return a},confirmSelected:function(){this.selectMaterialModalFlag=!1;var t=this.$u.deepClone(this.copayMyHolds);this.myHolds=t},hideSelectMaterialModal:function(){this.selectMaterialModalFlag=!1},showSelectMaterialModal:function(t){this.selectMaterial=t;var e=this.calcAvailableMaterialQuantity(t.materialId);if(0!=e){this.selectMaterialModalFlag=!0;var a=this.$u.deepClone(this.myHolds);this.copayMyHolds=a}else this.noAvailableMaterialFlag=!0},findActiveComposeActivityDetail:function(){var t=this;this.$u.get("/composeActivity/findActiveComposeActivityDetail",{id:t.id}).then((function(e){t.activityDetail=e.data,t.materials=e.data.materials}))},findAllMyHold:function(){var t=this;this.$u.get("/myArtwork/findAllMyHold").then((function(e){for(var a=e.data,i=0;i<a.length;i++)a[i].selectedFlag=!1;t.myHolds=a}))}}};e.default=i},"9bf8":function(t,e,a){"use strict";a.r(e);var i=a("9675"),n=a.n(i);for(var o in i)"default"!==o&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},a247:function(t,e,a){"use strict";var i=a("a47a"),n=a.n(i);n.a},a26f:function(t,e,a){"use strict";a("d81d"),a("a9e3"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"u-checkbox",props:{name:{type:[String,Number],default:""},shape:{type:String,default:""},value:{type:Boolean,default:!1},disabled:{type:[String,Boolean],default:""},labelDisabled:{type:[String,Boolean],default:""},activeColor:{type:String,default:""},iconSize:{type:[String,Number],default:""},labelSize:{type:[String,Number],default:""},size:{type:[String,Number],default:""}},data:function(){return{parentDisabled:!1,newParams:{}}},created:function(){this.parent=this.$u.$parent.call(this,"u-checkbox-group"),this.parent&&this.parent.children.push(this)},computed:{isDisabled:function(){return""!==this.disabled?this.disabled:!!this.parent&&this.parent.disabled},isLabelDisabled:function(){return""!==this.labelDisabled?this.labelDisabled:!!this.parent&&this.parent.labelDisabled},checkboxSize:function(){return this.size?this.size:this.parent?this.parent.size:34},checkboxIconSize:function(){return this.iconSize?this.iconSize:this.parent?this.parent.iconSize:20},elActiveColor:function(){return this.activeColor?this.activeColor:this.parent?this.parent.activeColor:"primary"},elShape:function(){return this.shape?this.shape:this.parent?this.parent.shape:"square"},iconStyle:function(){var t={};return this.elActiveColor&&this.value&&!this.isDisabled&&(t.borderColor=this.elActiveColor,t.backgroundColor=this.elActiveColor),t.width=this.$u.addUnit(this.checkboxSize),t.height=this.$u.addUnit(this.checkboxSize),t},iconColor:function(){return this.value?"#ffffff":"transparent"},iconClass:function(){var t=[];return t.push("u-checkbox__icon-wrap--"+this.elShape),1==this.value&&t.push("u-checkbox__icon-wrap--checked"),this.isDisabled&&t.push("u-checkbox__icon-wrap--disabled"),this.value&&this.isDisabled&&t.push("u-checkbox__icon-wrap--disabled--checked"),t.join(" ")},checkboxStyle:function(){var t={};return this.parent&&this.parent.width&&(t.width=this.parent.width,t.flex="0 0 ".concat(this.parent.width)),this.parent&&this.parent.wrap&&(t.width="100%",t.flex="0 0 100%"),t}},methods:{onClickLabel:function(){this.isLabelDisabled||this.isDisabled||this.setValue()},toggle:function(){this.isDisabled||this.setValue()},emitEvent:function(){var t=this;this.$emit("change",{value:!this.value,name:this.name}),setTimeout((function(){t.parent&&t.parent.emitEvent&&t.parent.emitEvent()}),80)},setValue:function(){var t=0;if(this.parent&&this.parent.children&&this.parent.children.map((function(e){e.value&&t++})),1==this.value)this.emitEvent(),this.$emit("input",!this.value);else{if(this.parent&&t>=this.parent.max)return this.$u.toast("最多可选".concat(this.parent.max,"项"));this.emitEvent(),this.$emit("input",!this.value)}}}};e.default=i},a47a:function(t,e,a){var i=a("298a");"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("7b16dc70",i,!0,{sourceMap:!1,shadowMode:!1})},ae11:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={uIcon:a("8ed9").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"u-checkbox",style:[t.checkboxStyle]},[a("v-uni-view",{staticClass:"u-checkbox__icon-wrap",class:[t.iconClass],style:[t.iconStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggle.apply(void 0,arguments)}}},[a("u-icon",{staticClass:"u-checkbox__icon-wrap__icon",attrs:{name:"checkbox-mark",size:t.checkboxIconSize,color:t.iconColor}})],1),a("v-uni-view",{staticClass:"u-checkbox__label",style:{fontSize:t.$u.addUnit(t.labelSize)},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClickLabel.apply(void 0,arguments)}}},[t._t("default")],2)],1)},o=[]},b495:function(t,e,a){"use strict";a("c975"),a("a9e3"),a("d3b7"),a("ac1f"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var t="";return t=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",t},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(t){var e=this;this.$u.throttle((function(){!0!==e.loading&&!0!==e.disabled&&(e.ripple&&(e.waveActive=!1,e.$nextTick((function(){this.getWaveQuery(t)}))),e.$emit("click",t))}),this.throttleTime)},getWaveQuery:function(t){var e=this;this.getElQuery().then((function(a){var i=a[0];if(i.width&&i.width&&(i.targetWidth=i.height>i.width?i.height:i.width,i.targetWidth)){e.fields=i;var n="",o="";n=t.touches[0].clientX,o=t.touches[0].clientY,e.rippleTop=o-i.top-i.targetWidth/2,e.rippleLeft=n-i.left-i.targetWidth/2,e.$nextTick((function(){e.waveActive=!0}))}}))},getElQuery:function(){var t=this;return new Promise((function(e){var a="";a=uni.createSelectorQuery().in(t),a.select(".u-btn").boundingClientRect(),a.exec((function(t){e(t)}))}))},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};e.default=i},b65d:function(t,e,a){var i=a("1530");"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("04d24e28",i,!0,{sourceMap:!1,shadowMode:!1})},bf63:function(t,e,a){var i=a("8de2");"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("02d2baee",i,!0,{sourceMap:!1,shadowMode:!1})},cc0d:function(t,e,a){"use strict";var i=a("b65d"),n=a.n(i);n.a},d84d:function(t,e,a){var i=a("52a3");"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("52ebce9a",i,!0,{sourceMap:!1,shadowMode:!1})},eb84:function(t,e,a){"use strict";a.r(e);var i=a("ae11"),n=a("0ef0");for(var o in n)"default"!==o&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("cc0d");var r,l=a("f0c5"),c=Object(l["a"])(n["default"],i["b"],i["c"],!1,null,"e356a272",null,!1,i["a"],r);e["default"]=c.exports},ee7b:function(t,e,a){"use strict";var i=a("d84d"),n=a.n(i);n.a},f1fb:function(t,e,a){"use strict";a.r(e);var i=a("b495"),n=a.n(i);for(var o in i)"default"!==o&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},f225:function(t,e,a){"use strict";a.r(e);var i=a("0011"),n=a.n(i);for(var o in i)"default"!==o&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a}}]);