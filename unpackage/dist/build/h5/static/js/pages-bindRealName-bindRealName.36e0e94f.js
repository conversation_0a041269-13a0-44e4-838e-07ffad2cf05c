(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-bindRealName-bindRealName"],{"031c":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={data:function(){return{customActionBtnStyle:{width:"70%"},realName:"",identityCard:"",mobile:""}},computed:{},onLoad:function(t){this.getMyPersonalInfo()},methods:{getMyPersonalInfo:function(){var t=this;this.$u.get("/member/getMyPersonalInfo").then((function(e){t.mobile=e.data.mobile}))},bindRealName:function(){var t=this;null!==t.realName&&""!==t.realName?null!==t.identityCard&&""!==t.identityCard?this.$u.post("/member/bindRealName",{realName:t.realName,identityCard:t.identityCard}).then((function(t){uni.showToast({icon:"success",title:"认证成功",duration:2e3,mask:!0,complete:function(){setTimeout((function(){uni.navigateBack()}),2e3)}})})):uni.showToast({title:"请输入身份证号",icon:"none"}):uni.showToast({title:"请输入真实姓名",icon:"none"})}}};e.default=n},1406:function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,".action-btn[data-v-6714e7d8]{padding-left:%?32?%;padding-right:%?32?%;padding-top:%?64?%}.info-form[data-v-6714e7d8]{padding-left:%?32?%;padding-right:%?32?%}.info-tips[data-v-6714e7d8]{background:#e3e3e3;margin:%?32?%;color:#838282;font-size:smaller;padding:%?32?% %?10?%}.info-tip1[data-v-6714e7d8]{padding-bottom:%?32?%}",""]),t.exports=e},1578:function(t,e,a){"use strict";a.r(e);var n=a("031c"),i=a.n(n);for(var o in n)"default"!==o&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},"4fe0":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={uForm:a("fc54").default,uFormItem:a("d51a").default,uInput:a("8fed").default,uButton:a("2e56").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("v-uni-view",{staticClass:"info-form"},[a("u-form",{staticClass:"transfer-form",attrs:{"border-bottom":!1}},[a("u-form-item",{attrs:{"label-position":"left",label:"真实姓名","label-width":"150","border-bottom":!1}},[a("u-input",{attrs:{placeholder:"请输入真实姓名",type:"text",clearable:!1},model:{value:t.realName,callback:function(e){t.realName=e},expression:"realName"}})],1),a("u-form-item",{attrs:{"label-position":"left",label:"身份证号","label-width":"150","border-bottom":!1}},[a("u-input",{attrs:{placeholder:"请输入身份证号",type:"text",clearable:!1},model:{value:t.identityCard,callback:function(e){t.identityCard=e},expression:"identityCard"}})],1),a("u-form-item",{attrs:{"label-position":"left",label:"联系方式","label-width":"150","border-bottom":!1}},[a("u-input",{attrs:{type:"text",clearable:!1,disabled:!0},model:{value:t.mobile,callback:function(e){t.mobile=e},expression:"mobile"}})],1)],1)],1),a("v-uni-view",{staticClass:"info-tips"},[a("v-uni-view",{staticClass:"info-tip info-tip1"},[t._v("根据法律法规要求，实名信息须与注册手机号持有人相符，且年满21周岁并小于60周岁。")]),a("v-uni-view",{staticClass:"info-tip"},[t._v("信息安全保障中，以下信息仅用于身份确认，未经您同意不会用于其它用途。")])],1),a("v-uni-view",{staticClass:"action-btn"},[a("u-button",{attrs:{"custom-style":t.customActionBtnStyle,type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.bindRealName.apply(void 0,arguments)}}},[t._v("立即认证")])],1)],1)},o=[]},5377:function(t,e,a){"use strict";a.r(e);var n=a("4fe0"),i=a("1578");for(var o in i)"default"!==o&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("c633");var l,r=a("f0c5"),u=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"6714e7d8",null,!1,n["a"],l);e["default"]=u.exports},"589c":function(t,e,a){var n=a("1406");"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("69de5322",n,!0,{sourceMap:!1,shadowMode:!1})},c633:function(t,e,a){"use strict";var n=a("589c"),i=a.n(n);i.a}}]);