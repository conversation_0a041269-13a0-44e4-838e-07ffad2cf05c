(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-login-login","pages-addSettlementAccount-addSettlementAccount~pages-bindRealName-bindRealName~pages-modifyPayPwd-m~62b78ebc"],{"003c":function(e,t,n){"use strict";n("a9e3"),n("ac1f"),n("5319"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={name:"u-verification-code",props:{seconds:{type:[String,Number],default:60},startText:{type:String,default:"获取验证码"},changeText:{type:String,default:"X秒重新获取"},endText:{type:String,default:"重新获取"},keepRunning:{type:Boolean,default:!1},uniqueKey:{type:String,default:""}},data:function(){return{secNum:this.seconds,timer:null,canGetCode:!0}},mounted:function(){this.checkKeepRunning()},watch:{seconds:{immediate:!0,handler:function(e){this.secNum=e}}},methods:{checkKeepRunning:function(){var e=Number(uni.getStorageSync(this.uniqueKey+"_$uCountDownTimestamp"));if(!e)return this.changeEvent(this.startText);var t=Math.floor(+new Date/1e3);this.keepRunning&&e&&e>t?(this.secNum=e-t,uni.removeStorageSync(this.uniqueKey+"_$uCountDownTimestamp"),this.start()):this.changeEvent(this.startText)},start:function(){var e=this;this.timer&&(clearInterval(this.timer),this.timer=null),this.$emit("start"),this.canGetCode=!1,this.changeEvent(this.changeText.replace(/x|X/,this.secNum)),this.setTimeToStorage(),this.timer=setInterval((function(){--e.secNum?e.changeEvent(e.changeText.replace(/x|X/,e.secNum)):(clearInterval(e.timer),e.timer=null,e.changeEvent(e.endText),e.secNum=e.seconds,e.$emit("end"),e.canGetCode=!0)}),1e3)},reset:function(){this.canGetCode=!0,clearInterval(this.timer),this.secNum=this.seconds,this.changeEvent(this.endText)},changeEvent:function(e){this.$emit("change",e)},setTimeToStorage:function(){if(this.keepRunning&&this.timer&&this.secNum>0&&this.secNum<=this.seconds){var e=Math.floor(+new Date/1e3);uni.setStorage({key:this.uniqueKey+"_$uCountDownTimestamp",data:e+Number(this.secNum)})}}},beforeDestroy:function(){this.setTimeToStorage(),clearTimeout(this.timer),this.timer=null}};t.default=i},"0077":function(e,t,n){"use strict";var i=n("5cc8"),a=n.n(i);a.a},"0568":function(e,t,n){var i=n("c7ab");"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=n("4f06").default;a("25dd8211",i,!0,{sourceMap:!1,shadowMode:!1})},"069d":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i}));var i={uIcon:n("8ed9").default},a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"u-input",class:{"u-input--border":e.border,"u-input--error":e.validateState},style:{padding:"0 "+(e.border?20:0)+"rpx",borderColor:e.borderColor,textAlign:e.inputAlign},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.inputClick.apply(void 0,arguments)}}},["textarea"==e.type?n("v-uni-textarea",{staticClass:"u-input__input u-input__textarea",style:[e.getStyle],attrs:{value:e.defaultValue,placeholder:e.placeholder,placeholderStyle:e.placeholderStyle,disabled:e.disabled,maxlength:e.inputMaxlength,fixed:e.fixed,focus:e.focus,autoHeight:e.autoHeight,"selection-end":e.uSelectionEnd,"selection-start":e.uSelectionStart,"cursor-spacing":e.getCursorSpacing,"show-confirm-bar":e.showConfirmbar},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.handleInput.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.handleBlur.apply(void 0,arguments)},focus:function(t){arguments[0]=t=e.$handleEvent(t),e.onFocus.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.onConfirm.apply(void 0,arguments)}}}):n("v-uni-input",{staticClass:"u-input__input",style:[e.getStyle],attrs:{type:"password"==e.type?"text":e.type,value:e.defaultValue,password:"password"==e.type&&!e.showPassword,placeholder:e.placeholder,placeholderStyle:e.placeholderStyle,disabled:e.disabled||"select"===e.type,maxlength:e.inputMaxlength,focus:e.focus,confirmType:e.confirmType,"cursor-spacing":e.getCursorSpacing,"selection-end":e.uSelectionEnd,"selection-start":e.uSelectionStart,"show-confirm-bar":e.showConfirmbar},on:{focus:function(t){arguments[0]=t=e.$handleEvent(t),e.onFocus.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.handleBlur.apply(void 0,arguments)},input:function(t){arguments[0]=t=e.$handleEvent(t),e.handleInput.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.onConfirm.apply(void 0,arguments)}}}),n("v-uni-view",{staticClass:"u-input__right-icon u-flex"},[e.clearable&&""!=e.value&&e.focused?n("v-uni-view",{staticClass:"u-input__right-icon__clear u-input__right-icon__item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClear.apply(void 0,arguments)}}},[n("u-icon",{attrs:{size:"32",name:"close-circle-fill",color:"#c0c4cc"}})],1):e._e(),e.passwordIcon&&"password"==e.type?n("v-uni-view",{staticClass:"u-input__right-icon__clear u-input__right-icon__item"},[n("u-icon",{attrs:{size:"32",name:e.showPassword?"eye-fill":"eye",color:"#c0c4cc"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showPassword=!e.showPassword}}})],1):e._e(),"select"==e.type?n("v-uni-view",{staticClass:"u-input__right-icon--select u-input__right-icon__item",class:{"u-input__right-icon--select--reverse":e.selectOpen}},[n("u-icon",{attrs:{name:"arrow-down-fill",size:"26",color:"#c0c4cc"}})],1):e._e()],1)],1)},r=[]},"12bd":function(e,t,n){var i=n("35f2");"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=n("4f06").default;a("0e6109e8",i,!0,{sourceMap:!1,shadowMode:!1})},"1ada":function(e,t,n){var i=n("24fb");t=i(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-00140dbc]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-00140dbc]{text-align:center}.pay-modal-amount-value[data-v-00140dbc]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-00140dbc]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-00140dbc]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-00140dbc]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-00140dbc]{font-size:%?36?%}.custom-pay-modal-btn[data-v-00140dbc]{width:80%}.u-image[data-v-00140dbc]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-00140dbc]{width:100%;height:100%}.u-image__loading[data-v-00140dbc], .u-image__error[data-v-00140dbc]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),e.exports=t},2058:function(e,t,n){"use strict";var i=n("12bd"),a=n.n(i);a.a},2290:function(e,t,n){var i=n("1ada");"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=n("4f06").default;a("49513309",i,!0,{sourceMap:!1,shadowMode:!1})},"28de":function(e,t,n){"use strict";var i=n("9b64"),a=n.n(i);a.a},"2a7e":function(e,t,n){"use strict";n.r(t);var i=n("9a1a"),a=n.n(i);for(var r in i)"default"!==r&&function(e){n.d(t,e,(function(){return i[e]}))}(r);t["default"]=a.a},"341a":function(e,t,n){"use strict";n.r(t);var i=n("ee0b"),a=n.n(i);for(var r in i)"default"!==r&&function(e){n.d(t,e,(function(){return i[e]}))}(r);t["default"]=a.a},"35f2":function(e,t,n){var i=n("24fb");t=i(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-0807932a]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-0807932a]{text-align:center}.pay-modal-amount-value[data-v-0807932a]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-0807932a]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-0807932a]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-0807932a]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-0807932a]{font-size:%?36?%}.custom-pay-modal-btn[data-v-0807932a]{width:80%}',""]),e.exports=t},"38c9":function(e,t,n){"use strict";var i=n("2290"),a=n.n(i);a.a},4362:function(e,t,n){t.nextTick=function(e){var t=Array.prototype.slice.call(arguments);t.shift(),setTimeout((function(){e.apply(null,t)}),0)},t.platform=t.arch=t.execPath=t.title="browser",t.pid=1,t.browser=!0,t.env={},t.argv=[],t.binding=function(e){throw new Error("No such module. (Possibly not yet loaded)")},function(){var e,i="/";t.cwd=function(){return i},t.chdir=function(t){e||(e=n("df7c")),i=e.resolve(t,i)}}(),t.exit=t.kill=t.umask=t.dlopen=t.uptime=t.memoryUsage=t.uvCounters=function(){},t.features={}},4396:function(e,t,n){"use strict";n.r(t);var i=n("bb2f"),a=n("a10f");for(var r in a)"default"!==r&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("9682");var o,u=n("f0c5"),s=Object(u["a"])(a["default"],i["b"],i["c"],!1,null,"068e6fc8",null,!1,i["a"],o);t["default"]=s.exports},"4c24":function(e,t,n){"use strict";n.r(t);var i=n("003c"),a=n.n(i);for(var r in i)"default"!==r&&function(e){n.d(t,e,(function(){return i[e]}))}(r);t["default"]=a.a},"4ca0":function(e,t,n){var i=n("24fb");t=i(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-7412e1a8]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-7412e1a8]{text-align:center}.pay-modal-amount-value[data-v-7412e1a8]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-7412e1a8]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-7412e1a8]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-7412e1a8]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-7412e1a8]{font-size:%?36?%}.custom-pay-modal-btn[data-v-7412e1a8]{width:80%}.u-code-wrap[data-v-7412e1a8]{width:0;height:0;position:fixed;z-index:-1}',""]),e.exports=t},5496:function(e,t,n){"use strict";(function(e){function i(){return i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},i.apply(this,arguments)}n("99af"),n("a623"),n("4160"),n("c975"),n("d81d"),n("fb6a"),n("a434"),n("a9e3"),n("b64b"),n("d3b7"),n("e25e"),n("4d63"),n("ac1f"),n("25f0"),n("466d"),n("5319"),n("159b"),n("ddb0"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=/%[sdj%]/g,r=function(){};function o(e){if(!e||!e.length)return null;var t={};return e.forEach((function(e){var n=e.field;t[n]=t[n]||[],t[n].push(e)})),t}function u(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=1,r=t[0],o=t.length;if("function"===typeof r)return r.apply(null,t.slice(1));if("string"===typeof r){for(var u=String(r).replace(a,(function(e){if("%%"===e)return"%";if(i>=o)return e;switch(e){case"%s":return String(t[i++]);case"%d":return Number(t[i++]);case"%j":try{return JSON.stringify(t[i++])}catch(n){return"[Circular]"}break;default:return e}})),s=t[i];i<o;s=t[++i])u+=" "+s;return u}return r}function s(e){return"string"===e||"url"===e||"hex"===e||"email"===e||"pattern"===e}function l(e,t){return void 0===e||null===e||(!("array"!==t||!Array.isArray(e)||e.length)||!(!s(t)||"string"!==typeof e||e))}function d(e,t,n){var i=[],a=0,r=e.length;function o(e){i.push.apply(i,e),a++,a===r&&n(i)}e.forEach((function(e){t(e,o)}))}function c(e,t,n){var i=0,a=e.length;function r(o){if(o&&o.length)n(o);else{var u=i;i+=1,u<a?t(e[u],r):n([])}}r([])}function f(e){var t=[];return Object.keys(e).forEach((function(n){t.push.apply(t,e[n])})),t}function p(e,t,n,i){if(t.first){var a=new Promise((function(t,a){var r=function(e){return i(e),e.length?a({errors:e,fields:o(e)}):t()},u=f(e);c(u,n,r)}));return a["catch"]((function(e){return e})),a}var r=t.firstFields||[];!0===r&&(r=Object.keys(e));var u=Object.keys(e),s=u.length,l=0,p=[],h=new Promise((function(t,a){var f=function(e){if(p.push.apply(p,e),l++,l===s)return i(p),p.length?a({errors:p,fields:o(p)}):t()};u.length||(i(p),t()),u.forEach((function(t){var i=e[t];-1!==r.indexOf(t)?c(i,n,f):d(i,n,f)}))}));return h["catch"]((function(e){return e})),h}function h(e){return function(t){return t&&t.message?(t.field=t.field||e.fullField,t):{message:"function"===typeof t?t():t,field:t.field||e.fullField}}}function m(e,t){if(t)for(var n in t)if(t.hasOwnProperty(n)){var a=t[n];"object"===typeof a&&"object"===typeof e[n]?e[n]=i({},e[n],{},a):e[n]=a}return e}function v(e,t,n,i,a,r){!e.required||n.hasOwnProperty(e.field)&&!l(t,r||e.type)||i.push(u(a.messages.required,e.fullField))}function g(e,t,n,i,a){(/^\s+$/.test(t)||""===t)&&i.push(u(a.messages.whitespace,e.fullField))}"undefined"!==typeof e&&Object({NODE_ENV:"production",VUE_APP_NAME:"nft",VUE_APP_PLATFORM:"h5",VUE_APP_INDEX_CSS_HASH:"2772579d",BASE_URL:"/"});var y={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,url:new RegExp("^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$","i"),hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i},b={integer:function(e){return b.number(e)&&parseInt(e,10)===e},float:function(e){return b.number(e)&&!b.integer(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch(t){return!1}},date:function(e){return"function"===typeof e.getTime&&"function"===typeof e.getMonth&&"function"===typeof e.getYear},number:function(e){return!isNaN(e)&&"number"===typeof+e},object:function(e){return"object"===typeof e&&!b.array(e)},method:function(e){return"function"===typeof e},email:function(e){return"string"===typeof e&&!!e.match(y.email)&&e.length<255},url:function(e){return"string"===typeof e&&!!e.match(y.url)},hex:function(e){return"string"===typeof e&&!!e.match(y.hex)}};function w(e,t,n,i,a){if(e.required&&void 0===t)v(e,t,n,i,a);else{var r=["integer","float","array","regexp","object","method","email","number","date","url","hex"],o=e.type;r.indexOf(o)>-1?b[o](t)||i.push(u(a.messages.types[o],e.fullField,e.type)):o&&typeof t!==e.type&&i.push(u(a.messages.types[o],e.fullField,e.type))}}function _(e,t,n,i,a){var r="number"===typeof e.len,o="number"===typeof e.min,s="number"===typeof e.max,l=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,d=t,c=null,f="number"===typeof t,p="string"===typeof t,h=Array.isArray(t);if(f?c="number":p?c="string":h&&(c="array"),!c)return!1;h&&(d=t.length),p&&(d=t.replace(l,"_").length),r?d!==e.len&&i.push(u(a.messages[c].len,e.fullField,e.len)):o&&!s&&d<e.min?i.push(u(a.messages[c].min,e.fullField,e.min)):s&&!o&&d>e.max?i.push(u(a.messages[c].max,e.fullField,e.max)):o&&s&&(d<e.min||d>e.max)&&i.push(u(a.messages[c].range,e.fullField,e.min,e.max))}var x="enum";function S(e,t,n,i,a){e[x]=Array.isArray(e[x])?e[x]:[],-1===e[x].indexOf(t)&&i.push(u(a.messages[x],e.fullField,e[x].join(", ")))}function C(e,t,n,i,a){if(e.pattern)if(e.pattern instanceof RegExp)e.pattern.lastIndex=0,e.pattern.test(t)||i.push(u(a.messages.pattern.mismatch,e.fullField,t,e.pattern));else if("string"===typeof e.pattern){var r=new RegExp(e.pattern);r.test(t)||i.push(u(a.messages.pattern.mismatch,e.fullField,t,e.pattern))}}var E={required:v,whitespace:g,type:w,range:_,enum:S,pattern:C};function q(e,t,n,i,a){var r=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(l(t,"string")&&!e.required)return n();E.required(e,t,i,r,a,"string"),l(t,"string")||(E.type(e,t,i,r,a),E.range(e,t,i,r,a),E.pattern(e,t,i,r,a),!0===e.whitespace&&E.whitespace(e,t,i,r,a))}n(r)}function T(e,t,n,i,a){var r=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(l(t)&&!e.required)return n();E.required(e,t,i,r,a),void 0!==t&&E.type(e,t,i,r,a)}n(r)}function O(e,t,n,i,a){var r=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(""===t&&(t=void 0),l(t)&&!e.required)return n();E.required(e,t,i,r,a),void 0!==t&&(E.type(e,t,i,r,a),E.range(e,t,i,r,a))}n(r)}function $(e,t,n,i,a){var r=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(l(t)&&!e.required)return n();E.required(e,t,i,r,a),void 0!==t&&E.type(e,t,i,r,a)}n(r)}function P(e,t,n,i,a){var r=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(l(t)&&!e.required)return n();E.required(e,t,i,r,a),l(t)||E.type(e,t,i,r,a)}n(r)}function j(e,t,n,i,a){var r=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(l(t)&&!e.required)return n();E.required(e,t,i,r,a),void 0!==t&&(E.type(e,t,i,r,a),E.range(e,t,i,r,a))}n(r)}function A(e,t,n,i,a){var r=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(l(t)&&!e.required)return n();E.required(e,t,i,r,a),void 0!==t&&(E.type(e,t,i,r,a),E.range(e,t,i,r,a))}n(r)}function k(e,t,n,i,a){var r=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(l(t,"array")&&!e.required)return n();E.required(e,t,i,r,a,"array"),l(t,"array")||(E.type(e,t,i,r,a),E.range(e,t,i,r,a))}n(r)}function F(e,t,n,i,a){var r=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(l(t)&&!e.required)return n();E.required(e,t,i,r,a),void 0!==t&&E.type(e,t,i,r,a)}n(r)}var I="enum";function B(e,t,n,i,a){var r=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(l(t)&&!e.required)return n();E.required(e,t,i,r,a),void 0!==t&&E[I](e,t,i,r,a)}n(r)}function N(e,t,n,i,a){var r=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(l(t,"string")&&!e.required)return n();E.required(e,t,i,r,a),l(t,"string")||E.pattern(e,t,i,r,a)}n(r)}function z(e,t,n,i,a){var r=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(l(t)&&!e.required)return n();var u;if(E.required(e,t,i,r,a),!l(t))u="number"===typeof t?new Date(t):t,E.type(e,u,i,r,a),u&&E.range(e,u.getTime(),i,r,a)}n(r)}function M(e,t,n,i,a){var r=[],o=Array.isArray(t)?"array":typeof t;E.required(e,t,i,r,a,o),n(r)}function R(e,t,n,i,a){var r=e.type,o=[],u=e.required||!e.required&&i.hasOwnProperty(e.field);if(u){if(l(t,r)&&!e.required)return n();E.required(e,t,i,o,a,r),l(t,r)||E.type(e,t,i,o,a)}n(o)}function V(e,t,n,i,a){var r=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(l(t)&&!e.required)return n();E.required(e,t,i,r,a)}n(r)}var L={string:q,method:T,number:O,boolean:$,regexp:P,integer:j,float:A,array:k,object:F,enum:B,pattern:N,date:z,url:R,hex:R,email:R,required:M,any:V};function D(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}var U=D();function W(e){this.rules=null,this._messages=U,this.define(e)}W.prototype={messages:function(e){return e&&(this._messages=m(D(),e)),this._messages},define:function(e){if(!e)throw new Error("Cannot configure a schema with no rules");if("object"!==typeof e||Array.isArray(e))throw new Error("Rules must be an object");var t,n;for(t in this.rules={},e)e.hasOwnProperty(t)&&(n=e[t],this.rules[t]=Array.isArray(n)?n:[n])},validate:function(e,t,n){var a=this;void 0===t&&(t={}),void 0===n&&(n=function(){});var r,s,l=e,d=t,c=n;if("function"===typeof d&&(c=d,d={}),!this.rules||0===Object.keys(this.rules).length)return c&&c(),Promise.resolve();function f(e){var t,n=[],i={};function a(e){var t;Array.isArray(e)?n=(t=n).concat.apply(t,e):n.push(e)}for(t=0;t<e.length;t++)a(e[t]);n.length?i=o(n):(n=null,i=null),c(n,i)}if(d.messages){var v=this.messages();v===U&&(v=D()),m(v,d.messages),d.messages=v}else d.messages=this.messages();var g={},y=d.keys||Object.keys(this.rules);y.forEach((function(t){r=a.rules[t],s=l[t],r.forEach((function(n){var r=n;"function"===typeof r.transform&&(l===e&&(l=i({},l)),s=l[t]=r.transform(s)),r="function"===typeof r?{validator:r}:i({},r),r.validator=a.getValidationMethod(r),r.field=t,r.fullField=r.fullField||t,r.type=a.getType(r),r.validator&&(g[t]=g[t]||[],g[t].push({rule:r,value:s,source:l,field:t}))}))}));var b={};return p(g,d,(function(e,t){var n,a=e.rule,r=("object"===a.type||"array"===a.type)&&("object"===typeof a.fields||"object"===typeof a.defaultField);function o(e,t){return i({},t,{fullField:a.fullField+"."+e})}function s(n){void 0===n&&(n=[]);var s=n;if(Array.isArray(s)||(s=[s]),!d.suppressWarning&&s.length&&W.warning("async-validator:",s),s.length&&a.message&&(s=[].concat(a.message)),s=s.map(h(a)),d.first&&s.length)return b[a.field]=1,t(s);if(r){if(a.required&&!e.value)return s=a.message?[].concat(a.message).map(h(a)):d.error?[d.error(a,u(d.messages.required,a.field))]:[],t(s);var l={};if(a.defaultField)for(var c in e.value)e.value.hasOwnProperty(c)&&(l[c]=a.defaultField);for(var f in l=i({},l,{},e.rule.fields),l)if(l.hasOwnProperty(f)){var p=Array.isArray(l[f])?l[f]:[l[f]];l[f]=p.map(o.bind(null,f))}var m=new W(l);m.messages(d.messages),e.rule.options&&(e.rule.options.messages=d.messages,e.rule.options.error=d.error),m.validate(e.value,e.rule.options||d,(function(e){var n=[];s&&s.length&&n.push.apply(n,s),e&&e.length&&n.push.apply(n,e),t(n.length?n:null)}))}else t(s)}r=r&&(a.required||!a.required&&e.value),a.field=e.field,a.asyncValidator?n=a.asyncValidator(a,e.value,s,e.source,d):a.validator&&(n=a.validator(a,e.value,s,e.source,d),!0===n?s():!1===n?s(a.message||a.field+" fails"):n instanceof Array?s(n):n instanceof Error&&s(n.message)),n&&n.then&&n.then((function(){return s()}),(function(e){return s(e)}))}),(function(e){f(e)}))},getType:function(e){if(void 0===e.type&&e.pattern instanceof RegExp&&(e.type="pattern"),"function"!==typeof e.validator&&e.type&&!L.hasOwnProperty(e.type))throw new Error(u("Unknown rule type %s",e.type));return e.type||"string"},getValidationMethod:function(e){if("function"===typeof e.validator)return e.validator;var t=Object.keys(e),n=t.indexOf("message");return-1!==n&&t.splice(n,1),1===t.length&&"required"===t[0]?L.required:L[this.getType(e)]||!1}},W.register=function(e,t){if("function"!==typeof t)throw new Error("Cannot register a validator by type, validator is not a function");L[e]=t},W.warning=r,W.messages=U;var H=W;t.default=H}).call(this,n("4362"))},"59be":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i}));var i={uIcon:n("8ed9").default},a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"u-image",style:[e.wrapStyle,e.backgroundStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClick.apply(void 0,arguments)}}},[e.isError?e._e():n("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==e.shape?"50%":e.$u.addUnit(e.borderRadius)},attrs:{src:e.src,mode:e.mode,"lazy-load":e.lazyLoad,"show-menu-by-longpress":e.showMenuByLongpress},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.onErrorHandler.apply(void 0,arguments)},load:function(t){arguments[0]=t=e.$handleEvent(t),e.onLoadHandler.apply(void 0,arguments)}}}),e.showLoading&&e.loading?n("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==e.shape?"50%":e.$u.addUnit(e.borderRadius),backgroundColor:this.bgColor}},[e.$slots.loading?e._t("loading"):n("u-icon",{attrs:{name:e.loadingIcon,width:e.width,height:e.height}})],2):e._e(),e.showError&&e.isError&&!e.loading?n("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==e.shape?"50%":e.$u.addUnit(e.borderRadius)}},[e.$slots.error?e._t("error"):n("u-icon",{attrs:{name:e.errorIcon,width:e.width,height:e.height}})],2):e._e()],1)},r=[]},"5cc8":function(e,t,n){var i=n("a407");"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=n("4f06").default;a("7b20b208",i,!0,{sourceMap:!1,shadowMode:!1})},"7afc":function(e,t,n){"use strict";var i;n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"u-form"},[e._t("default")],2)},r=[]},"7f8d":function(e,t,n){"use strict";n("c975"),n("d81d"),n("a9e3"),n("d3b7"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={name:"u-form",props:{model:{type:Object,default:function(){return{}}},errorType:{type:Array,default:function(){return["message","toast"]}},borderBottom:{type:Boolean,default:!0},labelPosition:{type:String,default:"left"},labelWidth:{type:[String,Number],default:90},labelAlign:{type:String,default:"left"},labelStyle:{type:Object,default:function(){return{}}}},provide:function(){return{uForm:this}},data:function(){return{rules:{}}},created:function(){this.fields=[]},methods:{setRules:function(e){this.rules=e},resetFields:function(){this.fields.map((function(e){e.resetField()}))},validate:function(e){var t=this;return new Promise((function(n){var i=!0,a=0,r=[];t.fields.map((function(o){o.validation("",(function(o){o&&(i=!1,r.push(o)),++a===t.fields.length&&(n(i),-1===t.errorType.indexOf("none")&&t.errorType.indexOf("toast")>=0&&r.length&&t.$u.toast(r[0]),"function"==typeof e&&e(i))}))}))}))}}};t.default=i},8087:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i}));var i={uIcon:n("8ed9").default},a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"u-form-item",class:{"u-border-bottom":e.elBorderBottom,"u-form-item__border-bottom--error":"error"===e.validateState&&e.showError("border-bottom")}},[n("v-uni-view",{staticClass:"u-form-item__body",style:{flexDirection:"left"==e.elLabelPosition?"row":"column"}},[n("v-uni-view",{staticClass:"u-form-item--left",style:{width:e.uLabelWidth,flex:"0 0 "+e.uLabelWidth,marginBottom:"left"==e.elLabelPosition?0:"10rpx"}},[e.required||e.leftIcon||e.label?n("v-uni-view",{staticClass:"u-form-item--left__content"},[e.required?n("v-uni-text",{staticClass:"u-form-item--left__content--required"},[e._v("*")]):e._e(),e.leftIcon?n("v-uni-view",{staticClass:"u-form-item--left__content__icon"},[n("u-icon",{attrs:{name:e.leftIcon,"custom-style":e.leftIconStyle}})],1):e._e(),n("v-uni-view",{staticClass:"u-form-item--left__content__label",style:[e.elLabelStyle,{"justify-content":"left"==e.elLabelAlign?"flex-start":"center"==e.elLabelAlign?"center":"flex-end"}]},[e._v(e._s(e.label))])],1):e._e()],1),n("v-uni-view",{staticClass:"u-form-item--right u-flex"},[n("v-uni-view",{staticClass:"u-form-item--right__content"},[n("v-uni-view",{staticClass:"u-form-item--right__content__slot "},[e._t("default")],2),e.$slots.right||e.rightIcon?n("v-uni-view",{staticClass:"u-form-item--right__content__icon u-flex"},[e.rightIcon?n("u-icon",{attrs:{"custom-style":e.rightIconStyle,name:e.rightIcon}}):e._e(),e._t("right")],2):e._e()],1)],1)],1),"error"===e.validateState&&e.showError("message")?n("v-uni-view",{staticClass:"u-form-item__message",style:{paddingLeft:"left"==e.elLabelPosition?e.$u.addUnit(e.elLabelWidth):"0"}},[e._v(e._s(e.validateMessage))]):e._e()],1)},r=[]},"8a76":function(e,t,n){"use strict";n.r(t);var i=n("f0fb"),a=n("4c24");for(var r in a)"default"!==r&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("8aa6");var o,u=n("f0c5"),s=Object(u["a"])(a["default"],i["b"],i["c"],!1,null,"7412e1a8",null,!1,i["a"],o);t["default"]=s.exports},"8aa6":function(e,t,n){"use strict";var i=n("ecd2"),a=n.n(i);a.a},"8fed":function(e,t,n){"use strict";n.r(t);var i=n("069d"),a=n("2a7e");for(var r in a)"default"!==r&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("28de");var o,u=n("f0c5"),s=Object(u["a"])(a["default"],i["b"],i["c"],!1,null,"5c361d36",null,!1,i["a"],o);t["default"]=s.exports},"93e8":function(e,t,n){"use strict";n.r(t);var i=n("59be"),a=n("f7ab");for(var r in a)"default"!==r&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("38c9");var o,u=n("f0c5"),s=Object(u["a"])(a["default"],i["b"],i["c"],!1,null,"00140dbc",null,!1,i["a"],o);t["default"]=s.exports},9682:function(e,t,n){"use strict";var i=n("0568"),a=n.n(i);a.a},"9a1a":function(e,t,n){"use strict";var i=n("4ea4");n("a9e3"),n("498a"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i(n("bc4e")),r={name:"u-input",mixins:[a.default],props:{value:{type:[String,Number],default:""},type:{type:String,default:"text"},inputAlign:{type:String,default:"left"},placeholder:{type:String,default:"请输入内容"},disabled:{type:Boolean,default:!1},maxlength:{type:[Number,String],default:140},placeholderStyle:{type:String,default:"color: #c0c4cc;"},confirmType:{type:String,default:"done"},customStyle:{type:Object,default:function(){return{}}},fixed:{type:Boolean,default:!1},focus:{type:Boolean,default:!1},passwordIcon:{type:Boolean,default:!0},border:{type:Boolean,default:!1},borderColor:{type:String,default:"#dcdfe6"},autoHeight:{type:Boolean,default:!0},selectOpen:{type:Boolean,default:!1},height:{type:[Number,String],default:""},clearable:{type:Boolean,default:!0},cursorSpacing:{type:[Number,String],default:0},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},trim:{type:Boolean,default:!0},showConfirmbar:{type:Boolean,default:!0}},data:function(){return{defaultValue:this.value,inputHeight:70,textareaHeight:100,validateState:!1,focused:!1,showPassword:!1,lastValue:""}},watch:{value:function(e,t){this.defaultValue=e,e!=t&&"select"==this.type&&this.handleInput({detail:{value:e}})}},computed:{inputMaxlength:function(){return Number(this.maxlength)},getStyle:function(){var e={};return e.minHeight=this.height?this.height+"rpx":"textarea"==this.type?this.textareaHeight+"rpx":this.inputHeight+"rpx",e=Object.assign(e,this.customStyle),e},getCursorSpacing:function(){return Number(this.cursorSpacing)},uSelectionStart:function(){return String(this.selectionStart)},uSelectionEnd:function(){return String(this.selectionEnd)}},created:function(){this.$on("on-form-item-error",this.onFormItemError)},methods:{handleInput:function(e){var t=this,n=e.detail.value;this.trim&&(n=this.$u.trim(n)),this.$emit("input",n),this.defaultValue=n,setTimeout((function(){t.dispatch("u-form-item","on-form-change",n)}),40)},handleBlur:function(e){var t=this;setTimeout((function(){t.focused=!1}),100),this.$emit("blur",e.detail.value),setTimeout((function(){t.dispatch("u-form-item","on-form-blur",e.detail.value)}),40)},onFormItemError:function(e){this.validateState=e},onFocus:function(e){this.focused=!0,this.$emit("focus")},onConfirm:function(e){this.$emit("confirm",e.detail.value)},onClear:function(e){this.$emit("input","")},inputClick:function(){this.$emit("click")}}};t.default=r},"9a86":function(e,t,n){var i=n("24fb");t=i(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-5c361d36]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-5c361d36]{text-align:center}.pay-modal-amount-value[data-v-5c361d36]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-5c361d36]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-5c361d36]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-5c361d36]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-5c361d36]{font-size:%?36?%}.custom-pay-modal-btn[data-v-5c361d36]{width:80%}.u-input[data-v-5c361d36]{position:relative;flex:1;display:flex;flex-direction:row}.u-input__input[data-v-5c361d36]{font-size:%?28?%;color:#303133;flex:1}.u-input__textarea[data-v-5c361d36]{width:auto;font-size:%?28?%;color:#303133;padding:%?10?% 0;line-height:normal;flex:1}.u-input--border[data-v-5c361d36]{border-radius:%?6?%;border-radius:4px;border:1px solid #dcdfe6}.u-input--error[data-v-5c361d36]{border-color:#fa3534!important}.u-input__right-icon__item[data-v-5c361d36]{margin-left:%?10?%}.u-input__right-icon--select[data-v-5c361d36]{transition:-webkit-transform .4s;transition:transform .4s;transition:transform .4s,-webkit-transform .4s}.u-input__right-icon--select--reverse[data-v-5c361d36]{-webkit-transform:rotate(-180deg);transform:rotate(-180deg)}',""]),e.exports=t},"9b64":function(e,t,n){var i=n("9a86");"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=n("4f06").default;a("26486a67",i,!0,{sourceMap:!1,shadowMode:!1})},a10f:function(e,t,n){"use strict";n.r(t);var i=n("d4e9"),a=n.n(i);for(var r in i)"default"!==r&&function(e){n.d(t,e,(function(){return i[e]}))}(r);t["default"]=a.a},a284:function(e,t,n){"use strict";n.r(t);var i=n("7f8d"),a=n.n(i);for(var r in i)"default"!==r&&function(e){n.d(t,e,(function(){return i[e]}))}(r);t["default"]=a.a},a407:function(e,t,n){var i=n("24fb");t=i(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-08a58aa2]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-08a58aa2]{text-align:center}.pay-modal-amount-value[data-v-08a58aa2]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-08a58aa2]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-08a58aa2]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-08a58aa2]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-08a58aa2]{font-size:%?36?%}.custom-pay-modal-btn[data-v-08a58aa2]{width:80%}.u-form-item[data-v-08a58aa2]{display:flex;flex-direction:row;padding:%?20?% 0;font-size:%?28?%;color:#303133;box-sizing:border-box;line-height:%?70?%;flex-direction:column}.u-form-item__border-bottom--error[data-v-08a58aa2]:after{border-color:#fa3534}.u-form-item__body[data-v-08a58aa2]{display:flex;flex-direction:row}.u-form-item--left[data-v-08a58aa2]{display:flex;flex-direction:row;align-items:center}.u-form-item--left__content[data-v-08a58aa2]{position:relative;display:flex;flex-direction:row;align-items:center;padding-right:%?10?%;flex:1}.u-form-item--left__content__icon[data-v-08a58aa2]{margin-right:%?8?%}.u-form-item--left__content--required[data-v-08a58aa2]{position:absolute;left:%?-16?%;vertical-align:middle;color:#fa3534;padding-top:%?6?%}.u-form-item--left__content__label[data-v-08a58aa2]{display:flex;flex-direction:row;align-items:center;flex:1}.u-form-item--right[data-v-08a58aa2]{flex:1}.u-form-item--right__content[data-v-08a58aa2]{display:flex;flex-direction:row;align-items:center;flex:1}.u-form-item--right__content__slot[data-v-08a58aa2]{flex:1;display:flex;flex-direction:row;align-items:center}.u-form-item--right__content__icon[data-v-08a58aa2]{margin-left:%?10?%;color:#c0c4cc;font-size:%?30?%}.u-form-item__message[data-v-08a58aa2]{font-size:%?24?%;line-height:%?24?%;color:#fa3534;margin-top:%?12?%}',""]),e.exports=t},a623:function(e,t,n){"use strict";var i=n("23e7"),a=n("b727").every,r=n("a640"),o=n("ae40"),u=r("every"),s=o("every");i({target:"Array",proto:!0,forced:!u||!s},{every:function(e){return a(this,e,arguments.length>1?arguments[1]:void 0)}})},bb2f:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i}));var i={uModal:n("4e29").default,uVerificationCode:n("8a76").default,uImage:n("93e8").default,uForm:n("fc54").default,uFormItem:n("d51a").default,uInput:n("8fed").default,uButton:n("2e56").default},a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",[n("u-modal",{attrs:{title:"提示","show-cancel-button":!1,"confirm-text":"我知道了"},model:{value:e.verificationCodeTipFlag,callback:function(t){e.verificationCodeTipFlag=t},expression:"verificationCodeTipFlag"}},[n("v-uni-view",{staticClass:"slot-content"},[n("v-uni-view",{staticClass:"verification-code-tip"},[n("v-uni-view",[e._v("验证码为：6666")]),n("v-uni-view",[e._v("输入后点击登录，即可体验完整功能")])],1)],1)],1),n("u-verification-code",{ref:"uCode",attrs:{seconds:"60"},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.codeChange.apply(void 0,arguments)}}}),n("v-uni-view",{staticClass:"login-page"},[n("v-uni-view",{staticClass:"u-flex u-row-center"},[n("u-image",{attrs:{width:"256rpx",height:"256rpx",src:"/static/img/c2c.png"}})],1),n("u-form",[n("u-form-item",{attrs:{"label-position":"top",label:"手机号","label-width":"150"}},[n("u-input",{attrs:{placeholder:"请输入11位手机号",type:"number",clearable:!1},model:{value:e.mobile,callback:function(t){e.mobile=t},expression:"mobile"}})],1),n("u-form-item",{attrs:{"label-position":"top",label:"验证码","label-width":"150"}},[n("u-input",{attrs:{placeholder:"请输入验证码",type:"text",clearable:!1},model:{value:e.verificationCode,callback:function(t){e.verificationCode=t},expression:"verificationCode"}}),n("u-button",{attrs:{slot:"right",size:"mini"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getCode.apply(void 0,arguments)}},slot:"right"},[e._v(e._s(e.codeTips))])],1),n("u-form-item",{attrs:{"label-position":"top",label:"邀请码(选填)","label-width":"150"}},[n("u-input",{attrs:{placeholder:"邀请码",type:"text",clearable:!1,disabled:e.inviteCodeReadonly},model:{value:e.inviteCode,callback:function(t){e.inviteCode=t},expression:"inviteCode"}})],1)],1),n("v-uni-view",{staticClass:"login-btn"},[n("u-button",{attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.login.apply(void 0,arguments)}}},[e._v("立即登录")])],1),n("v-uni-view",{staticClass:"login-tip"},[e._v("未注册的手机号验证后可自动登录")])],1)],1)},r=[]},c7ab:function(e,t,n){var i=n("24fb");t=i(!1),t.push([e.i,".verification-code-tip[data-v-068e6fc8]{padding-left:%?32?%;padding-right:%?32?%}.login-btn[data-v-068e6fc8]{padding-top:%?32?%}.welcome[data-v-068e6fc8]{color:#3c93ef;font-weight:700;font-size:28px;line-height:2}.login-tip[data-v-068e6fc8]{color:#888;text-align:center;padding-top:%?64?%;font-size:small}.login-page[data-v-068e6fc8]{padding-left:%?32?%;padding-right:%?32?%;padding-top:%?32?%}",""]),e.exports=t},d4e9:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{mobile:"",verificationCode:"",inviteCode:"",inviteCodeReadonly:!1,codeTips:"",verificationCodeTipFlag:!1}},onLoad:function(e){var t=e.inviteCode;t&&(this.inviteCode=t,this.inviteCodeReadonly=!0)},methods:{codeChange:function(e){this.codeTips=e},getCode:function(){var e=this,t=this;this.$refs.uCode.canGetCode&&(null!==t.mobile&&""!==t.mobile?11==t.mobile.length?(uni.showLoading({title:"正在获取验证码",mask:!0}),this.$u.get("/sendLoginVerificationCode",{mobile:t.mobile}).then((function(n){uni.hideLoading(),e.$refs.uCode.start(),t.verificationCodeTipFlag=!0}))):uni.showToast({title:"请输入11位的手机号",icon:"none"}):uni.showToast({title:"请输入手机号",icon:"none"}))},updatePushClientId:function(){var e=plus.push.getClientInfo();this.$u.post("/member/updatePushClientId",{pushClientId:e.clientid}).then((function(e){}))},login:function(){var e=this;null!==e.mobile&&""!==e.mobile?null!==e.verificationCode&&""!==e.verificationCode?this.$u.post("/login",{mobile:e.mobile,verificationCode:e.verificationCode,inviteCode:e.inviteCode}).then((function(e){var t=e.data;uni.setStorageSync("tokenName",t.tokenName),uni.setStorageSync("tokenValue",t.tokenValue),uni.setStorageSync("accountId",t.accountId),uni.reLaunch({url:"../home/<USER>"})})):uni.showToast({title:"请输入验证码",icon:"none"}):uni.showToast({title:"请输入手机号",icon:"none"})}}};t.default=i},d51a:function(e,t,n){"use strict";n.r(t);var i=n("8087"),a=n("341a");for(var r in a)"default"!==r&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("0077");var o,u=n("f0c5"),s=Object(u["a"])(a["default"],i["b"],i["c"],!1,null,"08a58aa2",null,!1,i["a"],o);t["default"]=s.exports},db34:function(e,t,n){"use strict";n("a9e3"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(e){e?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var e={};return e.width=this.$u.addUnit(this.width),e.height=this.$u.addUnit(this.height),e.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),e.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(e.opacity=this.opacity,e.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),e}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(e){this.loading=!1,this.isError=!0,this.$emit("error",e)},onLoadHandler:function(){var e=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){e.durationTime=e.duration,e.opacity=1,setTimeout((function(){e.removeBgColor()}),e.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};t.default=i},df7c:function(e,t,n){(function(e){function n(e,t){for(var n=0,i=e.length-1;i>=0;i--){var a=e[i];"."===a?e.splice(i,1):".."===a?(e.splice(i,1),n++):n&&(e.splice(i,1),n--)}if(t)for(;n--;n)e.unshift("..");return e}function i(e){"string"!==typeof e&&(e+="");var t,n=0,i=-1,a=!0;for(t=e.length-1;t>=0;--t)if(47===e.charCodeAt(t)){if(!a){n=t+1;break}}else-1===i&&(a=!1,i=t+1);return-1===i?"":e.slice(n,i)}function a(e,t){if(e.filter)return e.filter(t);for(var n=[],i=0;i<e.length;i++)t(e[i],i,e)&&n.push(e[i]);return n}t.resolve=function(){for(var t="",i=!1,r=arguments.length-1;r>=-1&&!i;r--){var o=r>=0?arguments[r]:e.cwd();if("string"!==typeof o)throw new TypeError("Arguments to path.resolve must be strings");o&&(t=o+"/"+t,i="/"===o.charAt(0))}return t=n(a(t.split("/"),(function(e){return!!e})),!i).join("/"),(i?"/":"")+t||"."},t.normalize=function(e){var i=t.isAbsolute(e),o="/"===r(e,-1);return e=n(a(e.split("/"),(function(e){return!!e})),!i).join("/"),e||i||(e="."),e&&o&&(e+="/"),(i?"/":"")+e},t.isAbsolute=function(e){return"/"===e.charAt(0)},t.join=function(){var e=Array.prototype.slice.call(arguments,0);return t.normalize(a(e,(function(e,t){if("string"!==typeof e)throw new TypeError("Arguments to path.join must be strings");return e})).join("/"))},t.relative=function(e,n){function i(e){for(var t=0;t<e.length;t++)if(""!==e[t])break;for(var n=e.length-1;n>=0;n--)if(""!==e[n])break;return t>n?[]:e.slice(t,n-t+1)}e=t.resolve(e).substr(1),n=t.resolve(n).substr(1);for(var a=i(e.split("/")),r=i(n.split("/")),o=Math.min(a.length,r.length),u=o,s=0;s<o;s++)if(a[s]!==r[s]){u=s;break}var l=[];for(s=u;s<a.length;s++)l.push("..");return l=l.concat(r.slice(u)),l.join("/")},t.sep="/",t.delimiter=":",t.dirname=function(e){if("string"!==typeof e&&(e+=""),0===e.length)return".";for(var t=e.charCodeAt(0),n=47===t,i=-1,a=!0,r=e.length-1;r>=1;--r)if(t=e.charCodeAt(r),47===t){if(!a){i=r;break}}else a=!1;return-1===i?n?"/":".":n&&1===i?"/":e.slice(0,i)},t.basename=function(e,t){var n=i(e);return t&&n.substr(-1*t.length)===t&&(n=n.substr(0,n.length-t.length)),n},t.extname=function(e){"string"!==typeof e&&(e+="");for(var t=-1,n=0,i=-1,a=!0,r=0,o=e.length-1;o>=0;--o){var u=e.charCodeAt(o);if(47!==u)-1===i&&(a=!1,i=o+1),46===u?-1===t?t=o:1!==r&&(r=1):-1!==t&&(r=-1);else if(!a){n=o+1;break}}return-1===t||-1===i||0===r||1===r&&t===i-1&&t===n+1?"":e.slice(t,i)};var r="b"==="ab".substr(-1)?function(e,t,n){return e.substr(t,n)}:function(e,t,n){return t<0&&(t=e.length+t),e.substr(t,n)}}).call(this,n("4362"))},ecd2:function(e,t,n){var i=n("4ca0");"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=n("4f06").default;a("eeee8754",i,!0,{sourceMap:!1,shadowMode:!1})},ee0b:function(e,t,n){"use strict";var i=n("4ea4");n("99af"),n("4de4"),n("c975"),n("d81d"),n("a434"),n("a9e3"),n("b64b"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i(n("ade3")),r=i(n("bc4e")),o=i(n("5496"));o.default.warning=function(){};var u={name:"u-form-item",mixins:[r.default],inject:{uForm:{default:function(){return null}}},props:{label:{type:String,default:""},prop:{type:String,default:""},borderBottom:{type:[String,Boolean],default:""},labelPosition:{type:String,default:""},labelWidth:{type:[String,Number],default:""},labelStyle:{type:Object,default:function(){return{}}},labelAlign:{type:String,default:""},rightIcon:{type:String,default:""},leftIcon:{type:String,default:""},leftIconStyle:{type:Object,default:function(){return{}}},rightIconStyle:{type:Object,default:function(){return{}}},required:{type:Boolean,default:!1}},data:function(){return{initialValue:"",validateState:"",validateMessage:"",errorType:["message"],fieldValue:"",parentData:{borderBottom:!0,labelWidth:90,labelPosition:"left",labelStyle:{},labelAlign:"left"}}},watch:{validateState:function(e){this.broadcastInputError()},"uForm.errorType":function(e){this.errorType=e,this.broadcastInputError()}},computed:{uLabelWidth:function(){return"left"==this.elLabelPosition?"true"===this.label||""===this.label?"auto":this.$u.addUnit(this.elLabelWidth):"100%"},showError:function(){var e=this;return function(t){return!(e.errorType.indexOf("none")>=0)&&e.errorType.indexOf(t)>=0}},elLabelWidth:function(){return 0!=this.labelWidth||""!=this.labelWidth?this.labelWidth:this.parentData.labelWidth?this.parentData.labelWidth:90},elLabelStyle:function(){return Object.keys(this.labelStyle).length?this.labelStyle:this.parentData.labelStyle?this.parentData.labelStyle:{}},elLabelPosition:function(){return this.labelPosition?this.labelPosition:this.parentData.labelPosition?this.parentData.labelPosition:"left"},elLabelAlign:function(){return this.labelAlign?this.labelAlign:this.parentData.labelAlign?this.parentData.labelAlign:"left"},elBorderBottom:function(){return""!==this.borderBottom?this.borderBottom:!this.parentData.borderBottom||this.parentData.borderBottom}},methods:{broadcastInputError:function(){this.broadcast("u-input","on-form-item-error","error"===this.validateState&&this.showError("border"))},setRules:function(){var e=this;this.$on("on-form-blur",e.onFieldBlur),this.$on("on-form-change",e.onFieldChange)},getRules:function(){var e=this.parent.rules;return e=e?e[this.prop]:[],[].concat(e||[])},onFieldBlur:function(){this.validation("blur")},onFieldChange:function(){this.validation("change")},getFilteredRule:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=this.getRules();return e?t.filter((function(t){return t.trigger&&-1!==t.trigger.indexOf(e)})):t},validation:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){};this.fieldValue=this.parent.model[this.prop];var i=this.getFilteredRule(e);if(!i||0===i.length)return n("");this.validateState="validating";var r=new o.default((0,a.default)({},this.prop,i));r.validate((0,a.default)({},this.prop,this.fieldValue),{firstFields:!0},(function(e,i){t.validateState=e?"error":"success",t.validateMessage=e?e[0].message:"",n(t.validateMessage)}))},resetField:function(){this.parent.model[this.prop]=this.initialValue,this.validateState="success"}},mounted:function(){var e=this;this.parent=this.$u.$parent.call(this,"u-form"),this.parent&&(Object.keys(this.parentData).map((function(t){e.parentData[t]=e.parent[t]})),this.prop&&(this.parent.fields.push(this),this.errorType=this.parent.errorType,this.initialValue=this.fieldValue,this.$nextTick((function(){e.setRules()}))))},beforeDestroy:function(){var e=this;this.parent&&this.prop&&this.parent.fields.map((function(t,n){t===e&&e.parent.fields.splice(n,1)}))}};t.default=u},f0fb:function(e,t,n){"use strict";var i;n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"u-code-wrap"})},r=[]},f7ab:function(e,t,n){"use strict";n.r(t);var i=n("db34"),a=n.n(i);for(var r in i)"default"!==r&&function(e){n.d(t,e,(function(){return i[e]}))}(r);t["default"]=a.a},fc54:function(e,t,n){"use strict";n.r(t);var i=n("7afc"),a=n("a284");for(var r in a)"default"!==r&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("2058");var o,u=n("f0c5"),s=Object(u["a"])(a["default"],i["b"],i["c"],!1,null,"0807932a",null,!1,i["a"],o);t["default"]=s.exports}}]);