(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-payOrderDetail-payOrderDetail"],{"02ce":function(t,e,a){"use strict";var i=a("e759"),o=a.n(i);o.a},"099a":function(t,e,a){"use strict";var i;a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+t.size,t.plain?"u-btn--"+t.type+"--plain":"",t.loading?"u-loading":"","circle"==t.shape?"u-round-circle":"",t.hairLine?t.showHairLineBorder:"u-btn--bold-border","u-btn--"+t.type,t.disabled?"u-btn--"+t.type+"--disabled":""],style:[t.customStyle,{overflow:t.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),disabled:t.disabled,"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":"sendMessagePath",lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.getHoverClass,loading:t.loading},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.getuserinfo.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.opensetting.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.launchapp.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click(e)}}},[t._t("default"),t.ripple?a("v-uni-view",{staticClass:"u-wave-ripple",class:[t.waveActive?"u-wave-active":""],style:{top:t.rippleTop+"px",left:t.rippleLeft+"px",width:t.fields.targetWidth+"px",height:t.fields.targetWidth+"px","background-color":t.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):t._e()],2)},r=[]},2758:function(t,e,a){"use strict";a("ac1f"),a("38cf"),a("5319"),a("1276"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={data:function(){return{showPayModalFlag:!1,showCancelModalFlag:!1,orderId:"",orderDetail:""}},onLoad:function(t){this.orderId=t.id,this.getOrderDetail()},methods:{confirmPay:function(){var t=this;this.$u.post("/transaction/confirmPay",{orderId:t.orderId}).then((function(e){uni.showToast({icon:"success",title:"支付成功!",duration:2e3,mask:!0,complete:function(){t.showPayModalFlag=!1,t.getOrderDetail()}})}))},cancelPay:function(){var t=this;this.$u.post("/transaction/cancelPay",{orderId:t.orderId}).then((function(e){uni.showToast({icon:"success",title:"取消成功!",duration:2e3,mask:!0,complete:function(){t.getOrderDetail()}})}))},moneyFormat:function(t,e){if(e=e||2,!t&&0!==t)return"";if(isNaN(+t))return"";if(0===t||"0"===t)return"0."+"0".repeat(e);var a=(t+"").split("."),i=a[0]?a[0]:0,o=a[1]?a[1]:0;return o=0===o?"0".repeat(e):(+("0."+o)).toFixed(e).split(".")[1],t=(i+"."+o).replace(/(\d{1,3})(?=(?:\d{3})+\.)/g,"$1,"),t},getOrderDetail:function(){var t=this;this.$u.get("/transaction/findMyPayOrderDetail",{id:t.orderId}).then((function(e){t.orderDetail=e.data}))}}};e.default=i},"2e56":function(t,e,a){"use strict";a.r(e);var i=a("099a"),o=a("f1fb");for(var r in o)"default"!==r&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("ee7b");var n,s=a("f0c5"),l=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"23f00fb2",null,!1,i["a"],n);e["default"]=l.exports},"52a3":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-23f00fb2]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-23f00fb2]{text-align:center}.pay-modal-amount-value[data-v-23f00fb2]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-23f00fb2]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-23f00fb2]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-23f00fb2]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-23f00fb2]{font-size:%?36?%}.custom-pay-modal-btn[data-v-23f00fb2]{width:80%}.u-btn[data-v-23f00fb2]::after{border:none}.u-btn[data-v-23f00fb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-23f00fb2]{border:1px solid #fff}.u-btn--default[data-v-23f00fb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-23f00fb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-23f00fb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-23f00fb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-23f00fb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-23f00fb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-23f00fb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-23f00fb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-23f00fb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-23f00fb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-23f00fb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-23f00fb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-23f00fb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-23f00fb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-23f00fb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-23f00fb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-23f00fb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-23f00fb2]{border-radius:%?100?%}.u-round-circle[data-v-23f00fb2]::after{border-radius:%?100?%}.u-loading[data-v-23f00fb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-23f00fb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-23f00fb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-23f00fb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-23f00fb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-23f00fb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-23f00fb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-23f00fb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-23f00fb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-23f00fb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-23f00fb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-23f00fb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-23f00fb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-23f00fb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-23f00fb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-23f00fb2]{background:#dd6161!important;color:#fff}',""]),t.exports=e},a00e:function(t,e,a){"use strict";a.r(e);var i=a("2758"),o=a.n(i);for(var r in i)"default"!==r&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},a7a2:function(t,e,a){"use strict";a.r(e);var i=a("b080"),o=a("a00e");for(var r in o)"default"!==r&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("02ce");var n,s=a("f0c5"),l=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"bfb0b102",null,!1,i["a"],n);e["default"]=l.exports},b080:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={uModal:a("4e29").default,uPopup:a("b7ad").default,uIcon:a("8ed9").default,uButton:a("2e56").default,uCountDown:a("f941").default,uImage:a("93e8").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"page-content"},[a("u-modal",{attrs:{title:"提示","show-cancel-button":!0,"cancel-text":"暂时不要","confirm-text":"取消交易",content:"是否要取消交易"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.cancelPay.apply(void 0,arguments)}},model:{value:t.showCancelModalFlag,callback:function(e){t.showCancelModalFlag=e},expression:"showCancelModalFlag"}}),a("u-popup",{attrs:{mode:"bottom","border-radius":"14",closeable:!0},model:{value:t.showPayModalFlag,callback:function(e){t.showPayModalFlag=e},expression:"showPayModalFlag"}},[a("v-uni-view",{staticClass:"pay-modal"},[a("v-uni-view",{staticClass:"pay-modal-amount"},[a("v-uni-text",[t._v("￥")]),a("v-uni-text",{staticClass:"pay-modal-amount-value"},[t._v(t._s(t.moneyFormat(t.orderDetail.amount)))])],1),a("v-uni-view",{staticClass:"pay-modal-pay-way-tip"},[t._v("选择支付方式")]),a("v-uni-view",{staticClass:"pay-modal-pay-ways"},[a("v-uni-view",{staticClass:"pay-modal-pay-way"},[a("v-uni-view",{staticClass:"pay-modal-pay-way-label"},[t._v("余额")]),a("v-uni-view",[a("u-icon",{attrs:{name:"checkmark-circle-fill",color:"#2979ff",size:"36"}})],1)],1)],1),a("v-uni-view",[a("u-button",{staticClass:"custom-pay-modal-btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmPay.apply(void 0,arguments)}}},[t._v("确认付款")])],1)],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"1"==t.orderDetail.state,expression:"orderDetail.state == '1'"}],staticClass:"order-state"},[a("v-uni-view",{staticClass:"payment-icon"},[a("u-icon",{attrs:{name:"clock",size:"140"}})],1),a("v-uni-view",{staticClass:"order-state-txt"},[t._v("待付款")]),a("v-uni-view",{staticClass:"payment-time-remaining"},[a("v-uni-text",{staticClass:"payment-time-remaining-l"},[t._v("支付剩余时间")]),a("u-count-down",{attrs:{"show-days":!1,"show-hours":!1,color:"#7f7f7f","separator-size":"26","separator-color":"#7f7f7f","font-size":"26",timestamp:t.orderDetail.surplusSecond}})],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"2"==t.orderDetail.state,expression:"orderDetail.state == '2'"}],staticClass:"order-state"},[a("v-uni-view",{staticClass:"success-icon"},[a("u-icon",{attrs:{name:"checkmark-circle",size:"140"}})],1),a("v-uni-view",{staticClass:"order-state-txt"},[t._v("交易成功")])],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"3"==t.orderDetail.state,expression:"orderDetail.state == '3'"}],staticClass:"order-state"},[a("v-uni-view",{staticClass:"cancel-icon"},[a("u-icon",{attrs:{name:"info-circle",size:"140"}})],1),a("v-uni-view",{staticClass:"order-state-txt"},[t._v("已取消")])],1),a("v-uni-view",{staticClass:"commodity-info"},[a("v-uni-view",{staticClass:"commodity-info-l"},[a("u-image",{attrs:{width:"130rpx",height:"200rpx","border-radius":"10",src:t.orderDetail.collectionCover}})],1),a("v-uni-view",{staticClass:"commodity-info-r"},[a("v-uni-view",{staticClass:"commodity-info-r1"},[t._v(t._s(t.orderDetail.collectionName))]),a("v-uni-view",{staticClass:"commodity-info-r2"},[t._v(t._s(t.orderDetail.creatorName))])],1)],1),a("v-uni-view",{staticClass:"order-items"},[a("v-uni-view",{staticClass:"order-item"},[a("v-uni-view",{staticClass:"order-item-label"},[t._v("订单金额")]),a("v-uni-view",{staticClass:"order-item-content"},[t._v(t._s(t.moneyFormat(t.orderDetail.amount))+"元")])],1),a("v-uni-view",{staticClass:"order-item"},[a("v-uni-view",{staticClass:"order-item-label"},[t._v("交易数量")]),a("v-uni-view",{staticClass:"order-item-content"},[t._v("1")])],1),a("v-uni-view",{staticClass:"order-item"},[a("v-uni-view",{staticClass:"order-item-label"},[t._v("创建时间")]),a("v-uni-view",{staticClass:"order-item-content"},[t._v(t._s(t.orderDetail.createTime))])],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"2"==t.orderDetail.state,expression:"orderDetail.state == '2'"}],staticClass:"order-item"},[a("v-uni-view",{staticClass:"order-item-label"},[t._v("付款时间")]),a("v-uni-view",{staticClass:"order-item-content"},[t._v(t._s(t.orderDetail.paidTime))])],1),a("v-uni-view",{staticClass:"order-item"},[a("v-uni-view",{staticClass:"order-item-label"},[t._v("订单编号")]),a("v-uni-view",{staticClass:"order-item-content"},[t._v(t._s(t.orderDetail.orderNo))])],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"2"==t.orderDetail.state,expression:"orderDetail.state == '2'"}],staticClass:"order-item"},[a("v-uni-view",{staticClass:"order-item-label"},[t._v("支付方式")]),a("v-uni-view",{staticClass:"order-item-content"},[t._v("余额")])],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"1"==t.orderDetail.state,expression:"orderDetail.state == '1'"}],staticClass:"order-record-actions"},[a("v-uni-view",{staticClass:"cancel-btn"},[a("u-button",{staticClass:"custom-btn",attrs:{type:"error"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showCancelModalFlag=!0}}},[t._v("取消订单")])],1),a("v-uni-view",{staticClass:"pay-btn"},[a("u-button",{staticClass:"custom-btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showPayModalFlag=!0}}},[t._v("去付款")])],1)],1)],1)},r=[]},b495:function(t,e,a){"use strict";a("c975"),a("a9e3"),a("d3b7"),a("ac1f"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var t="";return t=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",t},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(t){var e=this;this.$u.throttle((function(){!0!==e.loading&&!0!==e.disabled&&(e.ripple&&(e.waveActive=!1,e.$nextTick((function(){this.getWaveQuery(t)}))),e.$emit("click",t))}),this.throttleTime)},getWaveQuery:function(t){var e=this;this.getElQuery().then((function(a){var i=a[0];if(i.width&&i.width&&(i.targetWidth=i.height>i.width?i.height:i.width,i.targetWidth)){e.fields=i;var o="",r="";o=t.touches[0].clientX,r=t.touches[0].clientY,e.rippleTop=r-i.top-i.targetWidth/2,e.rippleLeft=o-i.left-i.targetWidth/2,e.$nextTick((function(){e.waveActive=!0}))}}))},getElQuery:function(){var t=this;return new Promise((function(e){var a="";a=uni.createSelectorQuery().in(t),a.select(".u-btn").boundingClientRect(),a.exec((function(t){e(t)}))}))},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};e.default=i},d84d:function(t,e,a){var i=a("52a3");"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("4f06").default;o("52ebce9a",i,!0,{sourceMap:!1,shadowMode:!1})},e71e:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,".order-items[data-v-bfb0b102]{padding-bottom:%?32?%}.order-item[data-v-bfb0b102]{display:flex;justify-content:space-between;padding-left:%?32?%;padding-right:%?32?%;line-height:2;font-size:small}.order-item-label[data-v-bfb0b102]{flex-basis:25%}.order-item-content[data-v-bfb0b102]{opacity:.6;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-align:right;color:#000}.order-record-actions[data-v-bfb0b102]{display:flex;justify-content:center;padding-top:%?32?%}.cancel-btn[data-v-bfb0b102]{padding-right:%?20?%}.pay-btn[data-v-bfb0b102]{padding-left:%?20?%}.custom-btn[data-v-bfb0b102]{width:%?220?%}.commodity-info[data-v-bfb0b102]{background:#e7e7e7;padding-top:%?10?%;padding-bottom:%?10?%;border-radius:%?20?%;font-size:small;display:flex;padding-left:%?40?%;padding-right:%?20?%;margin:%?32?%}.commodity-info-l[data-v-bfb0b102]{flex:1}.commodity-info-r[data-v-bfb0b102]{flex:2.5;display:flex;flex-direction:column;justify-content:space-between;padding-left:%?40?%}.commodity-info-r1[data-v-bfb0b102]{font-weight:700;font-size:larger}.commodity-info-r2[data-v-bfb0b102]{color:#7f7f7f}.order-state[data-v-bfb0b102]{text-align:center;padding-top:%?60?%}.success-icon[data-v-bfb0b102]{color:#3d9c6d}.payment-icon[data-v-bfb0b102]{color:#7f7f7f}.cancel-icon[data-v-bfb0b102]{color:#f90}.order-state-txt[data-v-bfb0b102]{line-height:2;font-size:large}.payment-time-remaining[data-v-bfb0b102]{font-size:smaller;color:#7f7f7f}.payment-time-remaining-l[data-v-bfb0b102]{padding-right:%?8?%}",""]),t.exports=e},e759:function(t,e,a){var i=a("e71e");"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("4f06").default;o("c92b3d8c",i,!0,{sourceMap:!1,shadowMode:!1})},ee7b:function(t,e,a){"use strict";var i=a("d84d"),o=a.n(i);o.a},f1fb:function(t,e,a){"use strict";a.r(e);var i=a("b495"),o=a.n(i);for(var r in i)"default"!==r&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a}}]);