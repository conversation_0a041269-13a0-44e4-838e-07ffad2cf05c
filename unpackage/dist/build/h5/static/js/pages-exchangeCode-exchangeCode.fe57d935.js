(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-exchangeCode-exchangeCode"],{"099a":function(t,e,a){"use strict";var n;a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return n}));var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+t.size,t.plain?"u-btn--"+t.type+"--plain":"",t.loading?"u-loading":"","circle"==t.shape?"u-round-circle":"",t.hairLine?t.showHairLineBorder:"u-btn--bold-border","u-btn--"+t.type,t.disabled?"u-btn--"+t.type+"--disabled":""],style:[t.customStyle,{overflow:t.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),disabled:t.disabled,"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":"sendMessagePath",lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.getHoverClass,loading:t.loading},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.getuserinfo.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.opensetting.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.launchapp.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click(e)}}},[t._t("default"),t.ripple?a("v-uni-view",{staticClass:"u-wave-ripple",class:[t.waveActive?"u-wave-active":""],style:{top:t.rippleTop+"px",left:t.rippleLeft+"px",width:t.fields.targetWidth+"px",height:t.fields.targetWidth+"px","background-color":t.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):t._e()],2)},i=[]},"2e56":function(t,e,a){"use strict";a.r(e);var n=a("099a"),o=a("f1fb");for(var i in o)"default"!==i&&function(t){a.d(e,t,(function(){return o[t]}))}(i);a("ee7b");var r,f=a("f0c5"),d=Object(f["a"])(o["default"],n["b"],n["c"],!1,null,"23f00fb2",null,!1,n["a"],r);e["default"]=d.exports},3074:function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,".mystery-box-result-content[data-v-cff04b7e]{text-align:center;padding-top:20px;padding-bottom:20px}.mystery-box-result-name[data-v-cff04b7e]{line-height:2;color:#888}.mystery-box-result-title[data-v-cff04b7e]{line-height:2;font-size:larger}.checked-flag[data-v-cff04b7e]{padding-bottom:%?32?%}.receiver-info[data-v-cff04b7e]{padding-top:%?32?%}.receiver-info-title[data-v-cff04b7e]{text-align:center}.receiver-info-item[data-v-cff04b7e]{font-size:small;padding-bottom:%?12?%;padding-top:%?12?%}.receiver-info-item-b[data-v-cff04b7e]{color:#888}.common-modal[data-v-cff04b7e]{padding-left:%?32?%;padding-right:%?32?%;padding-top:%?20?%;padding-bottom:%?20?%}.modal-title[data-v-cff04b7e]{display:flex;justify-content:space-between}.modal-title-txt[data-v-cff04b7e]{font-weight:700}.close-modal-txt[data-v-cff04b7e]{color:#909399}.give-explain[data-v-cff04b7e]{padding-left:%?32?%;padding-top:%?32?%;padding-right:%?32?%}.give-explain-title[data-v-cff04b7e]{line-height:2}.give-explain-items[data-v-cff04b7e]{font-size:smaller;color:#888}.action-btn[data-v-cff04b7e]{padding-left:%?32?%;padding-right:%?32?%;padding-top:%?20?%}.friend-account-tip[data-v-cff04b7e]{padding-top:%?64?%;padding-left:%?23?%;padding-bottom:%?20?%}.account-textarea-inner[data-v-cff04b7e]{padding-top:%?32?%}.account-textarea[data-v-cff04b7e]{background:#e7e7e7;padding-left:%?32?%;padding-right:%?32?%;margin-left:%?32?%;margin-right:%?32?%;border-radius:%?20?%;height:%?260?%}.sub-title[data-v-cff04b7e]{padding:%?32?%}.collection-info[data-v-cff04b7e]{padding-left:%?32?%;padding-right:%?32?%;background:#e7e7e7;margin-left:%?32?%;margin-right:%?32?%;display:flex;padding-top:%?16?%;padding-bottom:%?16?%;align-items:center;border-radius:%?20?%;font-size:small}.collection-info-r[data-v-cff04b7e]{padding-left:%?32?%;flex:1;width:0}.collection-name[data-v-cff04b7e]{font-size:larger}.collection-serial-number[data-v-cff04b7e]{line-height:2;color:#888}",""]),t.exports=e},"329b":function(t,e,a){"use strict";a.r(e);var n=a("8e48"),o=a.n(n);for(var i in n)"default"!==i&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=o.a},"472f":function(t,e,a){"use strict";var n=a("f9db"),o=a.n(n);o.a},"52a3":function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-23f00fb2]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-23f00fb2]{text-align:center}.pay-modal-amount-value[data-v-23f00fb2]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-23f00fb2]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-23f00fb2]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-23f00fb2]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-23f00fb2]{font-size:%?36?%}.custom-pay-modal-btn[data-v-23f00fb2]{width:80%}.u-btn[data-v-23f00fb2]::after{border:none}.u-btn[data-v-23f00fb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-23f00fb2]{border:1px solid #fff}.u-btn--default[data-v-23f00fb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-23f00fb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-23f00fb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-23f00fb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-23f00fb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-23f00fb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-23f00fb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-23f00fb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-23f00fb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-23f00fb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-23f00fb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-23f00fb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-23f00fb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-23f00fb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-23f00fb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-23f00fb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-23f00fb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-23f00fb2]{border-radius:%?100?%}.u-round-circle[data-v-23f00fb2]::after{border-radius:%?100?%}.u-loading[data-v-23f00fb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-23f00fb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-23f00fb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-23f00fb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-23f00fb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-23f00fb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-23f00fb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-23f00fb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-23f00fb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-23f00fb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-23f00fb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-23f00fb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-23f00fb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-23f00fb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-23f00fb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-23f00fb2]{background:#dd6161!important;color:#fff}',""]),t.exports=e},5365:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return n}));var n={uModal:a("4e29").default,uButton:a("2e56").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("u-modal",{attrs:{"show-title":!1,"show-cancel-button":!1,"confirm-text":"我知道了"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.toMyPage.apply(void 0,arguments)}},model:{value:t.exchangeResultFlag,callback:function(e){t.exchangeResultFlag=e},expression:"exchangeResultFlag"}},[a("v-uni-view",{staticClass:"slot-content"},[a("v-uni-view",{staticClass:"mystery-box-result-content"},[a("v-uni-view",{staticClass:"mystery-box-result-title"},[t._v("恭喜获得")]),a("v-uni-view",{staticClass:"mystery-box-result-name"},[t._v(t._s(t.exchangeResult.name))]),a("v-uni-image",{staticStyle:{width:"200rpx",height:"200rpx"},attrs:{src:t.exchangeResult.cover}})],1)],1)],1),a("v-uni-view",{staticClass:"friend-account-tip"},[t._v("请输入空投码:")]),a("v-uni-view",{staticClass:"account-textarea"},[a("v-uni-textarea",{staticClass:"account-textarea-inner",attrs:{placeholder:"请输入空投码"},model:{value:t.code,callback:function(e){t.code=e},expression:"code"}})],1),a("v-uni-view",{staticClass:"action-btn"},[a("u-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.exchange.apply(void 0,arguments)}}},[t._v("立即兑换")])],1),a("v-uni-view",{staticClass:"give-explain"},[a("v-uni-view",{staticClass:"give-explain-title"},[t._v("规则说明")]),a("v-uni-view",{staticClass:"give-explain-items"},[a("v-uni-view",{staticClass:"give-explain-item"},[t._v("1、一个空投码仅可使用一次，兑换码使用后即无效；")]),a("v-uni-view",{staticClass:"give-explain-item"},[t._v("2、空投码兑换成功后，藏品可在我的藏品里查看；")]),a("v-uni-view",{staticClass:"give-explain-item"},[t._v("3、严禁空投码场外交易，请不要向陌生人购买空投码，谨防诈骗盒恶意炒作；")])],1)],1)],1)},i=[]},"5bd2":function(t,e,a){"use strict";a.r(e);var n=a("5365"),o=a("329b");for(var i in o)"default"!==i&&function(t){a.d(e,t,(function(){return o[t]}))}(i);a("472f");var r,f=a("f0c5"),d=Object(f["a"])(o["default"],n["b"],n["c"],!1,null,"cff04b7e",null,!1,n["a"],r);e["default"]=d.exports},"8e48":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={data:function(){return{code:"",exchangeResultFlag:!1,exchangeResult:""}},onLoad:function(t){},methods:{toMyPage:function(){uni.reLaunch({url:"../my/my"})},exchange:function(){var t=this;if(null!==t.code&&""!==t.code){t=this;this.$u.post("/exchangeCode/exchange",{code:t.code}).then((function(e){t.exchangeResult=e.data,t.exchangeResultFlag=!0}))}else uni.showToast({title:"请输入空投码",icon:"none"})}}};e.default=n},b495:function(t,e,a){"use strict";a("c975"),a("a9e3"),a("d3b7"),a("ac1f"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var t="";return t=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",t},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(t){var e=this;this.$u.throttle((function(){!0!==e.loading&&!0!==e.disabled&&(e.ripple&&(e.waveActive=!1,e.$nextTick((function(){this.getWaveQuery(t)}))),e.$emit("click",t))}),this.throttleTime)},getWaveQuery:function(t){var e=this;this.getElQuery().then((function(a){var n=a[0];if(n.width&&n.width&&(n.targetWidth=n.height>n.width?n.height:n.width,n.targetWidth)){e.fields=n;var o="",i="";o=t.touches[0].clientX,i=t.touches[0].clientY,e.rippleTop=i-n.top-n.targetWidth/2,e.rippleLeft=o-n.left-n.targetWidth/2,e.$nextTick((function(){e.waveActive=!0}))}}))},getElQuery:function(){var t=this;return new Promise((function(e){var a="";a=uni.createSelectorQuery().in(t),a.select(".u-btn").boundingClientRect(),a.exec((function(t){e(t)}))}))},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};e.default=n},d84d:function(t,e,a){var n=a("52a3");"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=a("4f06").default;o("52ebce9a",n,!0,{sourceMap:!1,shadowMode:!1})},ee7b:function(t,e,a){"use strict";var n=a("d84d"),o=a.n(n);o.a},f1fb:function(t,e,a){"use strict";a.r(e);var n=a("b495"),o=a.n(n);for(var i in n)"default"!==i&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=o.a},f9db:function(t,e,a){var n=a("3074");"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=a("4f06").default;o("c3c18ce6",n,!0,{sourceMap:!1,shadowMode:!1})}}]);