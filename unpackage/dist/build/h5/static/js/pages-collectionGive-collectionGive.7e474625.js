(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-collectionGive-collectionGive"],{"0ef0":function(t,e,a){"use strict";a.r(e);var i=a("a26f"),n=a.n(i);for(var o in i)"default"!==o&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"12a9":function(t,e,a){var i=a("8f7f");"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("cd7bc260",i,!0,{sourceMap:!1,shadowMode:!1})},1530:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-e356a272]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-e356a272]{text-align:center}.pay-modal-amount-value[data-v-e356a272]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-e356a272]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-e356a272]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-e356a272]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-e356a272]{font-size:%?36?%}.custom-pay-modal-btn[data-v-e356a272]{width:80%}.u-checkbox[data-v-e356a272]{display:inline-flex;align-items:center;overflow:hidden;-webkit-user-select:none;user-select:none;line-height:1.8}.u-checkbox__icon-wrap[data-v-e356a272]{color:#606266;flex:none;display:-webkit-flex;display:flex;flex-direction:row;align-items:center;justify-content:center;box-sizing:border-box;width:%?42?%;height:%?42?%;color:transparent;text-align:center;transition-property:color,border-color,background-color;font-size:20px;border:1px solid #c8c9cc;transition-duration:.2s}.u-checkbox__icon-wrap--circle[data-v-e356a272]{border-radius:100%}.u-checkbox__icon-wrap--square[data-v-e356a272]{border-radius:%?6?%}.u-checkbox__icon-wrap--checked[data-v-e356a272]{color:#fff;background-color:#2979ff;border-color:#2979ff}.u-checkbox__icon-wrap--disabled[data-v-e356a272]{background-color:#ebedf0;border-color:#c8c9cc}.u-checkbox__icon-wrap--disabled--checked[data-v-e356a272]{color:#c8c9cc!important}.u-checkbox__label[data-v-e356a272]{word-wrap:break-word;margin-left:%?10?%;margin-right:%?24?%;color:#606266;font-size:%?30?%}.u-checkbox__label--disabled[data-v-e356a272]{color:#c8c9cc}',""]),t.exports=e},"1ada":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-00140dbc]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-00140dbc]{text-align:center}.pay-modal-amount-value[data-v-00140dbc]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-00140dbc]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-00140dbc]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-00140dbc]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-00140dbc]{font-size:%?36?%}.custom-pay-modal-btn[data-v-00140dbc]{width:80%}.u-image[data-v-00140dbc]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-00140dbc]{width:100%;height:100%}.u-image__loading[data-v-00140dbc], .u-image__error[data-v-00140dbc]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=e},"1d07":function(t,e,a){"use strict";a.r(e);var i=a("4c80"),n=a.n(i);for(var o in i)"default"!==o&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},2290:function(t,e,a){var i=a("1ada");"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("49513309",i,!0,{sourceMap:!1,shadowMode:!1})},"2cee":function(t,e,a){"use strict";a.r(e);var i=a("92df"),n=a("1d07");for(var o in n)"default"!==o&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("325b");var c,r=a("f0c5"),l=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"57136b01",null,!1,i["a"],c);e["default"]=l.exports},"325b":function(t,e,a){"use strict";var i=a("12a9"),n=a.n(i);n.a},"38c9":function(t,e,a){"use strict";var i=a("2290"),n=a.n(i);n.a},"3c11":function(t,e,a){"use strict";var i=a("7939"),n=a.n(i);n.a},"3da4":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,".checked-flag[data-v-41765ad2]{padding-bottom:%?32?%}.receiver-info[data-v-41765ad2]{padding-top:%?32?%}.receiver-info-title[data-v-41765ad2]{text-align:center}.receiver-info-item[data-v-41765ad2]{font-size:small;padding-bottom:%?12?%;padding-top:%?12?%}.receiver-info-item-b[data-v-41765ad2]{color:#888}.common-modal[data-v-41765ad2]{padding-left:%?32?%;padding-right:%?32?%;padding-top:%?20?%;padding-bottom:%?20?%}.modal-title[data-v-41765ad2]{display:flex;justify-content:space-between}.modal-title-txt[data-v-41765ad2]{font-weight:700}.close-modal-txt[data-v-41765ad2]{color:#909399}.give-explain[data-v-41765ad2]{padding-left:%?32?%;padding-top:%?32?%;padding-right:%?32?%}.give-explain-title[data-v-41765ad2]{line-height:2}.give-explain-items[data-v-41765ad2]{font-size:smaller;color:#888}.action-btn[data-v-41765ad2]{padding-left:%?32?%;padding-right:%?32?%;padding-top:%?20?%}.friend-account-tip[data-v-41765ad2]{padding-top:%?64?%;padding-left:%?23?%;padding-bottom:%?20?%}.account-textarea-inner[data-v-41765ad2]{padding-top:%?32?%}.account-textarea[data-v-41765ad2]{background:#e7e7e7;padding-left:%?32?%;padding-right:%?32?%;margin-left:%?32?%;margin-right:%?32?%;border-radius:%?20?%;height:%?260?%}.sub-title[data-v-41765ad2]{padding:%?32?%}.collection-info[data-v-41765ad2]{padding-left:%?32?%;padding-right:%?32?%;background:#e7e7e7;margin-left:%?32?%;margin-right:%?32?%;display:flex;padding-top:%?16?%;padding-bottom:%?16?%;align-items:center;border-radius:%?20?%;font-size:small}.collection-info-r[data-v-41765ad2]{padding-left:%?32?%;flex:1;width:0}.collection-name[data-v-41765ad2]{font-size:larger}.collection-serial-number[data-v-41765ad2]{line-height:2;color:#888}",""]),t.exports=e},"4c80":function(t,e,a){"use strict";var i=a("4ea4");a("d81d"),a("a9e3"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("bc4e")),o={name:"u-checkbox-group",mixins:[n.default],props:{max:{type:[Number,String],default:999},disabled:{type:Boolean,default:!1},name:{type:[Boolean,String],default:""},labelDisabled:{type:Boolean,default:!1},shape:{type:String,default:"square"},activeColor:{type:String,default:"#2979ff"},size:{type:[String,Number],default:34},width:{type:String,default:"auto"},wrap:{type:Boolean,default:!1},iconSize:{type:[String,Number],default:20}},data:function(){return{}},created:function(){this.children=[]},methods:{emitEvent:function(){var t=this,e=[];this.children.map((function(t){t.value&&e.push(t.name)})),this.$emit("change",e),setTimeout((function(){t.dispatch("u-form-item","on-form-change",e)}),60)}}};e.default=o},"59be":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={uIcon:a("8ed9").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():a("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad,"show-menu-by-longpress":t.showMenuByLongpress},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?a("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):a("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?a("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):a("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},o=[]},"5cb9":function(t,e,a){"use strict";a.r(e);var i=a("7935"),n=a("ad9e");for(var o in n)"default"!==o&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("3c11");var c,r=a("f0c5"),l=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"41765ad2",null,!1,i["a"],c);e["default"]=l.exports},7935:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={uPopup:a("b7ad").default,uIcon:a("8ed9").default,uCheckboxGroup:a("2cee").default,uCheckbox:a("eb84").default,uButton:a("2e56").default,uImage:a("93e8").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("u-popup",{attrs:{mode:"bottom","border-radius":"14"},model:{value:t.showConfirmModalFlag,callback:function(e){t.showConfirmModalFlag=e},expression:"showConfirmModalFlag"}},[a("v-uni-view",{staticClass:"common-modal"},[a("v-uni-view",{staticClass:"modal-title"},[a("v-uni-view",[a("u-icon",{attrs:{name:"arrow-leftward"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showConfirmModalFlag=!1}}})],1),a("v-uni-view",{staticClass:"modal-title-txt"},[t._v(t._s(t.collectionDetail.collectionName))]),a("v-uni-view",{staticClass:"close-modal-txt",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showConfirmModalFlag=!1}}},[t._v("关闭")])],1),a("v-uni-view",[a("v-uni-view",{staticClass:"receiver-info"},[a("v-uni-view",{staticClass:"receiver-info-title"},[t._v("受赠人信息")]),a("v-uni-view",{staticClass:"receiver-info-item"},[a("v-uni-view",{staticClass:"receiver-info-item-t"},[t._v("受赠人区块链地址:")]),a("v-uni-view",{staticClass:"receiver-info-item-b"},[t._v(t._s(t.receiverInfo.blockChainAddr))])],1),a("v-uni-view",{staticClass:"receiver-info-item"},[a("v-uni-view",{staticClass:"receiver-info-item-t"},[t._v("受赠人手机号:")]),a("v-uni-view",{staticClass:"receiver-info-item-b"},[t._v(t._s(t.receiverInfo.mobile))])],1)],1),a("v-uni-view",{staticClass:"checked-flag"},[a("u-checkbox-group",[a("u-checkbox",{attrs:{shape:"circle","label-size":"28"},model:{value:t.checkedFlag,callback:function(e){t.checkedFlag=e},expression:"checkedFlag"}},[t._v("本人承诺本次转赠仅用于好友之间交流分享")])],1)],1),a("u-button",{attrs:{type:"primary",disabled:!t.checkedFlag},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.collectionGive.apply(void 0,arguments)}}},[t._v("确认转赠")])],1)],1)],1),a("v-uni-view",{staticClass:"sub-title"},[t._v("将以下藏品转赠给好友")]),a("v-uni-view",{staticClass:"collection-info"},[a("v-uni-view",{staticClass:"collection-info-l"},[a("u-image",{staticClass:"collection-cover",attrs:{width:"160rpx",height:"160rpx","border-radius":"10",src:t.collectionDetail.collectionCover}})],1),a("v-uni-view",{staticClass:"collection-info-r"},[a("v-uni-view",{staticClass:"collection-name u-line-1"},[t._v(t._s(t.collectionDetail.collectionName))]),a("v-uni-view",{staticClass:"collection-serial-number"},[t._v("#\n\t\t\t\t"+t._s(t.collectionDetail.collectionSerialNumber)+"/"+t._s(t.collectionDetail.quantity))]),a("v-uni-view",{staticClass:"creator"},[t._v(t._s(t.collectionDetail.creatorName))])],1)],1),a("v-uni-view",{staticClass:"friend-account-tip"},[t._v("请输入好友的区块链地址/手机号:")]),a("v-uni-view",{staticClass:"account-textarea"},[a("v-uni-textarea",{staticClass:"account-textarea-inner",attrs:{placeholder:"请输入好友的区块链地址/手机号"},model:{value:t.giveToAccount,callback:function(e){t.giveToAccount=e},expression:"giveToAccount"}})],1),a("v-uni-view",{staticClass:"action-btn"},[a("u-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getCollectionReceiverInfo.apply(void 0,arguments)}}},[t._v("确认转赠")])],1),a("v-uni-view",{staticClass:"give-explain"},[a("v-uni-view",{staticClass:"give-explain-title"},[t._v("转赠说明")]),a("v-uni-view",{staticClass:"give-explain-items"},[a("v-uni-view",{staticClass:"give-explain-item"},[t._v("1、请您确认您具备赠送数字藏品的民事行为能力；")]),a("v-uni-view",{staticClass:"give-explain-item"},[t._v("2、请您确认您与受赠人均已通过平台的实名认证并遵守相关法律法规及平台协议；")]),a("v-uni-view",{staticClass:"give-explain-item"},[t._v("3、请您确认本次赠送行为未设定任何形式的对价；")]),a("v-uni-view",{staticClass:"give-explain-item"},[t._v("4、转赠操作无法撤销；")]),a("v-uni-view",{staticClass:"give-explain-item"},[t._v("5、与数字藏品相关的权利将会同步且毫无保留地转移至受赠人；")])],1)],1)],1)},o=[]},7939:function(t,e,a){var i=a("3da4");"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("3018f29f",i,!0,{sourceMap:!1,shadowMode:!1})},"8f7f":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-57136b01]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-57136b01]{text-align:center}.pay-modal-amount-value[data-v-57136b01]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-57136b01]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-57136b01]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-57136b01]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-57136b01]{font-size:%?36?%}.custom-pay-modal-btn[data-v-57136b01]{width:80%}.u-checkbox-group[data-v-57136b01]{display:inline-flex;flex-wrap:wrap}',""]),t.exports=e},"92df":function(t,e,a){"use strict";var i;a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"u-checkbox-group u-clearfix"},[t._t("default")],2)},o=[]},"93e8":function(t,e,a){"use strict";a.r(e);var i=a("59be"),n=a("f7ab");for(var o in n)"default"!==o&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("38c9");var c,r=a("f0c5"),l=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"00140dbc",null,!1,i["a"],c);e["default"]=l.exports},a26f:function(t,e,a){"use strict";a("d81d"),a("a9e3"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"u-checkbox",props:{name:{type:[String,Number],default:""},shape:{type:String,default:""},value:{type:Boolean,default:!1},disabled:{type:[String,Boolean],default:""},labelDisabled:{type:[String,Boolean],default:""},activeColor:{type:String,default:""},iconSize:{type:[String,Number],default:""},labelSize:{type:[String,Number],default:""},size:{type:[String,Number],default:""}},data:function(){return{parentDisabled:!1,newParams:{}}},created:function(){this.parent=this.$u.$parent.call(this,"u-checkbox-group"),this.parent&&this.parent.children.push(this)},computed:{isDisabled:function(){return""!==this.disabled?this.disabled:!!this.parent&&this.parent.disabled},isLabelDisabled:function(){return""!==this.labelDisabled?this.labelDisabled:!!this.parent&&this.parent.labelDisabled},checkboxSize:function(){return this.size?this.size:this.parent?this.parent.size:34},checkboxIconSize:function(){return this.iconSize?this.iconSize:this.parent?this.parent.iconSize:20},elActiveColor:function(){return this.activeColor?this.activeColor:this.parent?this.parent.activeColor:"primary"},elShape:function(){return this.shape?this.shape:this.parent?this.parent.shape:"square"},iconStyle:function(){var t={};return this.elActiveColor&&this.value&&!this.isDisabled&&(t.borderColor=this.elActiveColor,t.backgroundColor=this.elActiveColor),t.width=this.$u.addUnit(this.checkboxSize),t.height=this.$u.addUnit(this.checkboxSize),t},iconColor:function(){return this.value?"#ffffff":"transparent"},iconClass:function(){var t=[];return t.push("u-checkbox__icon-wrap--"+this.elShape),1==this.value&&t.push("u-checkbox__icon-wrap--checked"),this.isDisabled&&t.push("u-checkbox__icon-wrap--disabled"),this.value&&this.isDisabled&&t.push("u-checkbox__icon-wrap--disabled--checked"),t.join(" ")},checkboxStyle:function(){var t={};return this.parent&&this.parent.width&&(t.width=this.parent.width,t.flex="0 0 ".concat(this.parent.width)),this.parent&&this.parent.wrap&&(t.width="100%",t.flex="0 0 100%"),t}},methods:{onClickLabel:function(){this.isLabelDisabled||this.isDisabled||this.setValue()},toggle:function(){this.isDisabled||this.setValue()},emitEvent:function(){var t=this;this.$emit("change",{value:!this.value,name:this.name}),setTimeout((function(){t.parent&&t.parent.emitEvent&&t.parent.emitEvent()}),80)},setValue:function(){var t=0;if(this.parent&&this.parent.children&&this.parent.children.map((function(e){e.value&&t++})),1==this.value)this.emitEvent(),this.$emit("input",!this.value);else{if(this.parent&&t>=this.parent.max)return this.$u.toast("最多可选".concat(this.parent.max,"项"));this.emitEvent(),this.$emit("input",!this.value)}}}};e.default=i},ad9e:function(t,e,a){"use strict";a.r(e);var i=a("c236"),n=a.n(i);for(var o in i)"default"!==o&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},ae11:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={uIcon:a("8ed9").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"u-checkbox",style:[t.checkboxStyle]},[a("v-uni-view",{staticClass:"u-checkbox__icon-wrap",class:[t.iconClass],style:[t.iconStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggle.apply(void 0,arguments)}}},[a("u-icon",{staticClass:"u-checkbox__icon-wrap__icon",attrs:{name:"checkbox-mark",size:t.checkboxIconSize,color:t.iconColor}})],1),a("v-uni-view",{staticClass:"u-checkbox__label",style:{fontSize:t.$u.addUnit(t.labelSize)},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClickLabel.apply(void 0,arguments)}}},[t._t("default")],2)],1)},o=[]},b65d:function(t,e,a){var i=a("1530");"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("04d24e28",i,!0,{sourceMap:!1,shadowMode:!1})},c236:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={data:function(){return{collectionId:"",collectionDetail:"",giveToAccount:"",receiverInfo:"",showConfirmModalFlag:!1,checkedFlag:!1}},onLoad:function(t){this.collectionId=t.id,this.getCollectionDetail()},methods:{collectionGive:function(){var t=this;this.$u.post("/transaction/collectionGive",{giveToAccount:t.giveToAccount,holdCollectionId:t.collectionId}).then((function(t){uni.showToast({icon:"success",title:"转赠成功!",duration:2e3,mask:!0,complete:function(){setTimeout((function(){uni.reLaunch({url:"../my/my"})}),2e3)}})}))},getCollectionReceiverInfo:function(){var t=this;null!==t.giveToAccount&&""!==t.giveToAccount?this.$u.get("/transaction/getCollectionReceiverInfo",{giveToAccount:t.giveToAccount}).then((function(e){t.receiverInfo=e.data,t.showConfirmModalFlag=!0,t.checkedFlag=!1})):uni.showToast({title:"请输入好友的区块链地址/手机号",icon:"none"})},getCollectionDetail:function(){var t=this;this.$u.get("/myArtwork/findMyHoldCollectionDetail",{id:t.collectionId}).then((function(e){t.collectionDetail=e.data}))}}};e.default=i},cc0d:function(t,e,a){"use strict";var i=a("b65d"),n=a.n(i);n.a},db34:function(t,e,a){"use strict";a("a9e3"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:500},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=i},eb84:function(t,e,a){"use strict";a.r(e);var i=a("ae11"),n=a("0ef0");for(var o in n)"default"!==o&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("cc0d");var c,r=a("f0c5"),l=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"e356a272",null,!1,i["a"],c);e["default"]=l.exports},f7ab:function(t,e,a){"use strict";a.r(e);var i=a("db34"),n=a.n(i);for(var o in i)"default"!==o&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a}}]);