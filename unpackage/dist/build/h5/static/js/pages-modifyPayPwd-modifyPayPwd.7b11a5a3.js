(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-modifyPayPwd-modifyPayPwd"],{"003c":function(t,e,n){"use strict";n("a9e3"),n("ac1f"),n("5319"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"u-verification-code",props:{seconds:{type:[String,Number],default:60},startText:{type:String,default:"获取验证码"},changeText:{type:String,default:"X秒重新获取"},endText:{type:String,default:"重新获取"},keepRunning:{type:Boolean,default:!1},uniqueKey:{type:String,default:""}},data:function(){return{secNum:this.seconds,timer:null,canGetCode:!0}},mounted:function(){this.checkKeepRunning()},watch:{seconds:{immediate:!0,handler:function(t){this.secNum=t}}},methods:{checkKeepRunning:function(){var t=Number(uni.getStorageSync(this.uniqueKey+"_$uCountDownTimestamp"));if(!t)return this.changeEvent(this.startText);var e=Math.floor(+new Date/1e3);this.keepRunning&&t&&t>e?(this.secNum=t-e,uni.removeStorageSync(this.uniqueKey+"_$uCountDownTimestamp"),this.start()):this.changeEvent(this.startText)},start:function(){var t=this;this.timer&&(clearInterval(this.timer),this.timer=null),this.$emit("start"),this.canGetCode=!1,this.changeEvent(this.changeText.replace(/x|X/,this.secNum)),this.setTimeToStorage(),this.timer=setInterval((function(){--t.secNum?t.changeEvent(t.changeText.replace(/x|X/,t.secNum)):(clearInterval(t.timer),t.timer=null,t.changeEvent(t.endText),t.secNum=t.seconds,t.$emit("end"),t.canGetCode=!0)}),1e3)},reset:function(){this.canGetCode=!0,clearInterval(this.timer),this.secNum=this.seconds,this.changeEvent(this.endText)},changeEvent:function(t){this.$emit("change",t)},setTimeToStorage:function(){if(this.keepRunning&&this.timer&&this.secNum>0&&this.secNum<=this.seconds){var t=Math.floor(+new Date/1e3);uni.setStorage({key:this.uniqueKey+"_$uCountDownTimestamp",data:t+Number(this.secNum)})}}},beforeDestroy:function(){this.setTimeToStorage(),clearTimeout(this.timer),this.timer=null}};e.default=i},"0db1":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uVerificationCode:n("8a76").default,uPopup:n("b7ad").default,uForm:n("fc54").default,uFormItem:n("d51a").default,uInput:n("8fed").default,uButton:n("2e56").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("u-verification-code",{ref:"uCode",attrs:{seconds:"60"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.codeChange.apply(void 0,arguments)}}}),n("u-popup",{attrs:{mode:"bottom","border-radius":"14","mask-close-able":!1},model:{value:t.showReconfirmModal,callback:function(e){t.showReconfirmModal=e},expression:"showReconfirmModal"}},[n("v-uni-view",{staticClass:"reconfirm-popup"},[n("v-uni-view",{staticClass:"popup-title"},[n("v-uni-view",{staticClass:"popup-title-l"},[t._v("安全验证")]),n("v-uni-view",{staticClass:"popup-title-r",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showReconfirmModal=!1}}},[t._v("取消")])],1),n("v-uni-view",[n("u-form",[n("u-form-item",{attrs:{"label-position":"top",label:"","label-width":"150"}},[n("u-input",{attrs:{placeholder:"请输入验证码",type:"text",clearable:!1},model:{value:t.verificationCode,callback:function(e){t.verificationCode=e},expression:"verificationCode"}}),n("u-button",{attrs:{slot:"right",type:"success",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getCode.apply(void 0,arguments)}},slot:"right"},[t._v(t._s(t.codeTips))])],1)],1),n("u-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.modifyPwdInner.apply(void 0,arguments)}}},[t._v("确认")])],1)],1)],1),n("v-uni-view",{staticClass:"page-content"},[n("u-form",[n("u-form-item",{attrs:{"label-position":"top",label:"输入新支付密码","label-width":"150"}},[n("u-input",{attrs:{placeholder:"8-15位,且首位必须为字母",type:"password",clearable:!1},model:{value:t.newPwd,callback:function(e){t.newPwd=e},expression:"newPwd"}})],1),n("u-form-item",{attrs:{"label-position":"top",label:"确认新密码","label-width":"150"}},[n("u-input",{attrs:{placeholder:"请确认您的支付密码",type:"password",clearable:!1},model:{value:t.confirmPwd,callback:function(e){t.confirmPwd=e},expression:"confirmPwd"}})],1)],1)],1),n("v-uni-view",{staticClass:"fixed-bottom"},[n("u-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.modifyPwd.apply(void 0,arguments)}}},[t._v("确认")])],1)],1)},o=[]},"0de1":function(t,e,n){"use strict";var i=n("4c55"),a=n.n(i);a.a},"2d0c":function(t,e,n){"use strict";n.r(e);var i=n("0db1"),a=n("d300");for(var o in a)"default"!==o&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("0de1");var s,c=n("f0c5"),u=Object(c["a"])(a["default"],i["b"],i["c"],!1,null,"c5199f7a",null,!1,i["a"],s);e["default"]=u.exports},"4c24":function(t,e,n){"use strict";n.r(e);var i=n("003c"),a=n.n(i);for(var o in i)"default"!==o&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"4c55":function(t,e,n){var i=n("c26f");"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("207d0378",i,!0,{sourceMap:!1,shadowMode:!1})},"4ca0":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量\n * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可\n * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 \n */.pay-modal[data-v-7412e1a8]{padding-top:%?60?%;padding-bottom:%?60?%}.pay-modal-amount[data-v-7412e1a8]{text-align:center}.pay-modal-amount-value[data-v-7412e1a8]{font-size:%?60?%}.pay-modal-pay-way-tip[data-v-7412e1a8]{text-align:center;font-size:small;line-height:2;padding-bottom:%?16?%}.pay-modal-pay-ways[data-v-7412e1a8]{padding-left:%?32?%;padding-right:%?32?%;padding-bottom:%?96?%}.pay-modal-pay-way[data-v-7412e1a8]{display:flex;align-items:center;justify-content:space-between}.pay-modal-pay-way-label[data-v-7412e1a8]{font-size:%?36?%}.custom-pay-modal-btn[data-v-7412e1a8]{width:80%}.u-code-wrap[data-v-7412e1a8]{width:0;height:0;position:fixed;z-index:-1}',""]),t.exports=e},"8a76":function(t,e,n){"use strict";n.r(e);var i=n("f0fb"),a=n("4c24");for(var o in a)"default"!==o&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("8aa6");var s,c=n("f0c5"),u=Object(c["a"])(a["default"],i["b"],i["c"],!1,null,"7412e1a8",null,!1,i["a"],s);e["default"]=u.exports},"8aa6":function(t,e,n){"use strict";var i=n("ecd2"),a=n.n(i);a.a},c26f:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,".page-content[data-v-c5199f7a]{padding-left:%?32?%;padding-right:%?32?%}.reconfirm-popup[data-v-c5199f7a]{padding-left:%?32?%;padding-right:%?32?%;padding-top:%?20?%;padding-bottom:%?20?%}.popup-title[data-v-c5199f7a]{display:flex;justify-content:space-between}.popup-title-l[data-v-c5199f7a]{font-weight:700}.popup-title-r[data-v-c5199f7a]{color:#909399}.fixed-bottom[data-v-c5199f7a]{position:fixed;bottom:%?60?%;left:%?0?%;width:100%;padding-left:%?32?%;padding-right:%?32?%}",""]),t.exports=e},d300:function(t,e,n){"use strict";n.r(e);var i=n("db94"),a=n.n(i);for(var o in i)"default"!==o&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},db94:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={data:function(){return{newPwd:"",confirmPwd:"",showReconfirmModal:!1,verificationCode:"",codeTips:""}},methods:{codeChange:function(t){this.codeTips=t},getCode:function(){var t=this;this.$refs.uCode.canGetCode&&(uni.showLoading({title:"正在获取验证码",mask:!0}),this.$u.get("/member/sendModifyPayPwdVerificationCode",{}).then((function(e){uni.hideLoading(),uni.showToast({title:"验证码已发送",icon:"none"}),t.$refs.uCode.start()})))},modifyPwdInner:function(){var t=this;null!=t.verificationCode&&""!=t.verificationCode?this.$u.post("/member/modifyPayPwd",{newPwd:t.newPwd,verificationCode:t.verificationCode}).then((function(t){uni.showToast({icon:"success",title:"密码修改成功!",duration:2e3,complete:function(){setTimeout((function(){uni.navigateBack()}),2e3)}})})):uni.showToast({title:"请输入验证码",icon:"none"})},modifyPwd:function(){var t=this;if(null!=t.newPwd&&""!=t.newPwd)if(null!=t.confirmPwd&&""!=t.confirmPwd)if(t.newPwd==t.confirmPwd){var e=/^[A-Za-z][A-Za-z0-9]{5,14}$/;e.test(t.newPwd)?(t.showReconfirmModal=!0,t.verificationCode=""):uni.showToast({title:"请输入以字母开头,长度为6-15个字母和数字的密码",icon:"none"})}else uni.showToast({title:"密码不一致",icon:"none"});else uni.showToast({title:"请再输入新支付密码",icon:"none"});else uni.showToast({title:"请输入新支付密码",icon:"none"})}}};e.default=i},ecd2:function(t,e,n){var i=n("4ca0");"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("eeee8754",i,!0,{sourceMap:!1,shadowMode:!1})},f0fb:function(t,e,n){"use strict";var i;n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"u-code-wrap"})},o=[]}}]);