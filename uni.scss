/**
 * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量
 * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可
 * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 
 */
@import 'uview-ui/theme.scss';



.pay-modal {
		padding-top: 60rpx;
		padding-bottom: 60rpx;
	}

	.pay-modal-amount {
		text-align: center;

	}

	.pay-modal-amount-value {
		font-size: 60rpx;
	}

	.pay-modal-pay-way-tip {
		text-align: center;
		font-size: small;
		line-height: 2;
		padding-bottom: 16rpx;
	}

	.pay-modal-pay-ways {
		padding-left: 32rpx;
		padding-right: 32rpx;
		padding-bottom: 96rpx;
	}

	.pay-modal-pay-way {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.pay-modal-pay-way-label {
		font-size: 36rpx;
	}

	.custom-pay-modal-btn {
		width: 80%;
	}

