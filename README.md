拖把数藏客户端代码

# 声明

！！！本项目非开源项目，禁止发布到任何公开的 git 仓库中，如需自行 git 管理，请发布到私密仓库！一经发现，将立即移除权限，并追究法律责任！

# 常用地址

🔥🔥🔥项目介绍及购买地址：[http://nfturbo.wiki/](http://nfturbo.wiki/)

🔥项目交流群：[https://thoughts.aliyun.com/workspaces/6655879cf459b7001ba42f1b/docs/6673c2f45e11940001c785cc](https://thoughts.aliyun.com/workspaces/6655879cf459b7001ba42f1b/docs/6673c2f45e11940001c785cc)

🔥代码地址：[https://codeup.aliyun.com/66263f57d833774c93cc0418/NFTurbo](https://codeup.aliyun.com/66263f57d833774c93cc0418/NFTurbo)

🔥课程学习地址（视频+文档）：[https://aoybo.xet.tech/s/2prw1D](https://aoybo.xet.tech/s/2prw1D)

🔥学习讨论地址：[https://wx.zsxq.com/dweb2/index/group/88888288882252](https://wx.zsxq.com/dweb2/index/group/88888288882252)

🔥阿里云服务器优惠地址：[https://thoughts.aliyun.com/workspaces/6655879cf459b7001ba42f1b/docs/667054050f232c0001a545fb](https://thoughts.aliyun.com/workspaces/6655879cf459b7001ba42f1b/docs/667054050f232c0001a545fb)

🔥常见问题：[https://thoughts.aliyun.com/workspaces/6655879cf459b7001ba42f1b/folders/6673f1c82d397600011bd921](https://thoughts.aliyun.com/workspaces/6655879cf459b7001ba42f1b/folders/6673f1c82d397600011bd921)



# 部署说明

下载 HBuilder

## 本地部署(测试环境)
1. 打开工程目录
2. 启动 - 运行到 Chrome


## 线上部署(生产环境)
1. 打开工程目录
2. 右击项目 - 发行 - 网站/PC Web 或 H5
构建对应的脚本和样式文件，挂在到 HTML 页即可正常运行； 如考虑性能可对接 CDN